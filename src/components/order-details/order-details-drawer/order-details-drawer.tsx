'use client';

import { OrderDetailsContent } from '@/components/order-details/order-details-content';
import type { OrderEntity } from '@/core.constants';
import type { UserType } from '@/core.constants';
import { useRootContext } from '@/root-context';

import { OrderDetailsBaseDrawer } from './index';

interface OrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  userType?: UserType;
  onOrderAction?: () => void;
  hideActionButton?: boolean; // For activity page where we don't want to show buy button
}

export function OrderDetailsDrawer({
  open,
  onOpenChange,
  order,
  userType,
  onOrderAction,
  hideActionButton = false,
}: OrderDetailsDrawerProps) {
  const { collections } = useRootContext();

  const collection = order
    ? collections.find((c) => c.id === order.collectionId) || null
    : null;

  const handleDrawerClose = () => {
    onOpenChange(false);
  };

  const handleOrderActionWithClose = () => {
    onOpenChange(false);
    if (onOrderAction) {
      onOrderAction();
    }
  };

  if (!order) return null;

  return (
    <OrderDetailsBaseDrawer open={open} onOpenChange={onOpenChange}>
      <OrderDetailsContent
        order={order}
        collection={collection}
        userType={userType}
        onOrderAction={handleOrderActionWithClose}
        hideActionButton={hideActionButton}
        onClose={handleDrawerClose}
        showCloseButton
      />
    </OrderDetailsBaseDrawer>
  );
}

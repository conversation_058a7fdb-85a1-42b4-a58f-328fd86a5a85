# Fix Firebase Functions IAM Permissions for Custom Token Creation

## The Problem
Your Telegram authentication function is failing with this error:
```
Firebase service account lacks required IAM permissions for custom token creation. Please grant 'Service Account Token Creator' role.
```

## Solution Options

### Option 1: Using Google Cloud Console (Recommended)

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/
   - Select your project: `marketplace-dev-76a4a`

2. **Navigate to IAM & Admin**
   - Go to IAM & Admin > IAM
   - Or direct link: https://console.cloud.google.com/iam-admin/iam?project=marketplace-dev-76a4a

3. **Find your Firebase Functions Service Account**
   - Look for: `<EMAIL>`
   - This is the default service account used by Firebase Functions

4. **Edit the Service Account**
   - Click the pencil icon (Edit) next to the service account
   - Click "ADD ANOTHER ROLE"

5. **Add Required Roles**
   Add these roles one by one:
   - `Service Account Token Creator` (roles/iam.serviceAccountTokenCreator)
   - `Firebase Admin SDK Admin Service Agent` (roles/firebase.sdkAdminServiceAgent)
   - `Service Account User` (roles/iam.serviceAccountUser)

6. **Save Changes**
   - Click "SAVE" to apply the changes

### Option 2: Using gcloud CLI Commands

If you have gcloud CLI installed, run these commands:

```bash
# Set your project
gcloud config set project marketplace-dev-76a4a

# Grant Service Account Token Creator role
gcloud projects add-iam-policy-binding marketplace-dev-76a4a \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/iam.serviceAccountTokenCreator"

# Grant Firebase Admin SDK Admin Service Agent role
gcloud projects add-iam-policy-binding marketplace-dev-76a4a \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/firebase.sdkAdminServiceAgent"

# Grant Service Account User role
gcloud projects add-iam-policy-binding marketplace-dev-76a4a \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/iam.serviceAccountUser"
```

### Option 3: Install gcloud CLI

If you don't have gcloud CLI:

1. **Install gcloud CLI**
   - macOS: `brew install google-cloud-sdk`
   - Or download from: https://cloud.google.com/sdk/docs/install

2. **Authenticate**
   ```bash
   gcloud auth login
   gcloud config set project marketplace-dev-76a4a
   ```

3. **Run the commands from Option 2**

## Verification

After setting up the permissions:

1. **Test the function again**
   ```bash
   node test-telegram-auth.js
   ```

2. **Check if you get a successful response with a custom token**

3. **Verify permissions in console**
   ```bash
   gcloud projects get-iam-policy marketplace-dev-76a4a \
       --flatten="bindings[].members" \
       --format="table(bindings.role)" \
       --filter="bindings.members:<EMAIL>"
   ```

## What These Roles Do

- **Service Account Token Creator**: Allows creating custom authentication tokens
- **Firebase Admin SDK Admin Service Agent**: Provides broader Firebase Admin SDK permissions
- **Service Account User**: Allows the service account to act as other service accounts

## Next Steps

Once the IAM permissions are set up:

1. The Telegram authentication function will work properly
2. Custom tokens will be created successfully
3. Users can authenticate with Telegram in your app

## Troubleshooting

If you still get errors after setting up permissions:

1. Wait 5-10 minutes for permissions to propagate
2. Redeploy the function: `firebase deploy --only functions:signInWithTelegram`
3. Check the Firebase Functions logs in the console
4. Ensure you're using the correct project ID

## Security Note

These permissions are necessary for Firebase Functions to create custom authentication tokens. They follow Firebase's recommended security practices and are required for the `admin.auth().createCustomToken()` method to work.

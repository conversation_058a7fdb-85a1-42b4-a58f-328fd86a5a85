import { getTelegramBotToken, isDevelopment } from "../config";
import { log } from "../utils/logger";

export function verifyBotToken(providedToken: string) {
  try {
    const isDevMode = isDevelopment();

    if (isDevMode) {
      return true;
    }

    const expectedToken = getTelegramBotToken();

    if (!providedToken || !expectedToken) {
      return false;
    }

    return providedToken === expectedToken;
  } catch (error) {
    log.error("Error verifying bot token", error, {
      operation: "bot_token_verification",
    });
    return false;
  }
}

export function extractBotTokenFromHeader(authHeader: string | undefined) {
  if (!authHeader) {
    return null;
  }

  const parts = authHeader.split(" ");
  if (parts.length !== 2 || parts[0] !== "Bot") {
    return null;
  }

  return parts[1];
}

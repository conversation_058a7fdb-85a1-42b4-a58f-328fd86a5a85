import { defineMessages } from 'react-intl';

export const orderStatusUtilsMessages = defineMessages({
  // Status labels
  active: {
    id: 'orderStatusUtils.active',
    defaultMessage: 'Active',
  },
  paid: {
    id: 'orderStatusUtils.paid',
    defaultMessage: 'Paid',
  },
  giftSentToRelayer: {
    id: 'orderStatusUtils.giftSentToRelayer',
    defaultMessage: 'Sent to Bot',
  },
  fulfilled: {
    id: 'orderStatusUtils.fulfilled',
    defaultMessage: 'Fulfilled',
  },
  cancelled: {
    id: 'orderStatusUtils.cancelled',
    defaultMessage: 'Cancelled',
  },

  // Deadline titles
  timeToSendGift: {
    id: 'orderStatusUtils.timeToSendGift',
    defaultMessage: 'Time to Send Gift',
  },
  sellerDeadline: {
    id: 'orderStatusUtils.sellerDeadline',
    defaultMessage: 'Seller Deadline',
  },
  timeToClaimGift: {
    id: 'orderStatusUtils.timeToClaimGift',
    defaultMessage: 'Time to Claim Gift',
  },
  buyerDeadline: {
    id: 'orderStatusUtils.buyerDeadline',
    defaultMessage: 'Buyer Deadline',
  },
  deadline: {
    id: 'orderStatusUtils.deadline',
    defaultMessage: 'Deadline',
  },

  // Deadline descriptions
  sellerMustSend: {
    id: 'orderStatusUtils.sellerMustSend',
    defaultMessage: 'Seller must send',
  },
  sendOrLoseCollateral: {
    id: 'orderStatusUtils.sendOrLoseCollateral',
    defaultMessage: 'Send or lose collateral',
  },
  sendGiftToRelayerOrLoseCollateral: {
    id: 'orderStatusUtils.sendGiftToRelayerOrLoseCollateral',
    defaultMessage: 'Send gift to relayer or lose collateral',
  },
  sellerMustSendGiftOrLoseCollateral: {
    id: 'orderStatusUtils.sellerMustSendGiftOrLoseCollateral',
    defaultMessage: 'Seller must send gift or lose collateral',
  },
  claimGiftFromRelayerOrLoseCollateral: {
    id: 'orderStatusUtils.claimGiftFromRelayerOrLoseCollateral',
    defaultMessage: 'Claim gift from relayer or lose collateral',
  },
  buyerMustClaimGiftOrLoseCollateral: {
    id: 'orderStatusUtils.buyerMustClaimGiftOrLoseCollateral',
    defaultMessage: 'Buyer must claim gift or lose collateral',
  },
});

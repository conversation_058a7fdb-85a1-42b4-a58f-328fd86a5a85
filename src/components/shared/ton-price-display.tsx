import { TonLogo } from '@/components/TonLogo';
import { APP_CURRENCY } from '@/core.constants';

interface TonPriceDisplayProps {
  amount: number;
  size?: number;
  className?: string;
  tonLogoClassName?: string;
  showUnit?: boolean;
}

export function TonPriceDisplay({
  amount,
  size = 24,
  className = '',
  tonLogoClassName = '',
  showUnit = false,
}: TonPriceDisplayProps) {
  const formattedAmount =
    typeof amount === 'number' ? amount.toFixed(2) : amount;

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <span>{formattedAmount}</span>
      <TonLogo size={size} className={tonLogoClassName} />
      {showUnit && (
        <span className="text-sm text-[#708499]">{APP_CURRENCY}</span>
      )}
    </div>
  );
}

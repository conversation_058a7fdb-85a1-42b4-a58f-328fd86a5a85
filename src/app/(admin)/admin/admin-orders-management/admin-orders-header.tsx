import { DollarSign, Loader2, Plus, Trash2, <PERSON><PERSON>ir<PERSON> } from 'lucide-react';
import { useState } from 'react';

import { deleteAllActiveAdminOrders } from '@/api/admin-api';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

import { AdminOrdersBatchCreateModal } from './admin-orders-batch-create-modal';
import { IncreaseUserBalancesModal } from './increase-user-balances-modal';

interface AdminOrdersHeaderProps {
  onRefresh: () => Promise<void>;
  selectedOrderIds: Set<string>;
  onBatchCancel: (orderIds: string[]) => Promise<void>;
  onBatchDelete: (orderIds: string[]) => Promise<void>;
}

export function AdminOrdersHeader({
  onRefresh,
  selectedOrderIds,
  onBatchCancel,
  onBatchDelete,
}: AdminOrdersHeaderProps) {
  const { toast } = useToast();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isIncreaseBalancesModalOpen, setIsIncreaseBalancesModalOpen] =
    useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isBatchCanceling, setIsBatchCanceling] = useState(false);
  const [isBatchDeleting, setIsBatchDeleting] = useState(false);

  const isDevelopment = process.env.NODE_ENV === 'development';
  const selectedCount = selectedOrderIds.size;

  const handleBatchCreateSuccess = async () => {
    setIsCreateModalOpen(false);
    await onRefresh();
  };

  const handleDeleteActiveOrders = async () => {
    try {
      setIsDeleting(true);
      const deletedCount = await deleteAllActiveAdminOrders();

      toast({
        title: 'Success',
        description: `Deleted ${deletedCount} active admin orders`,
      });

      await onRefresh();
    } catch (error) {
      console.error('Error deleting active admin orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete active admin orders',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleBatchCancel = async () => {
    try {
      setIsBatchCanceling(true);
      const orderIds = Array.from(selectedOrderIds);
      await onBatchCancel(orderIds);

      toast({
        title: 'Success',
        description: `Cancelled ${orderIds.length} orders`,
      });
    } catch (error) {
      console.error('Error cancelling orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to cancel selected orders',
        variant: 'destructive',
      });
    } finally {
      setIsBatchCanceling(false);
    }
  };

  const handleBatchDelete = async () => {
    try {
      setIsBatchDeleting(true);
      const orderIds = Array.from(selectedOrderIds);
      await onBatchDelete(orderIds);

      toast({
        title: 'Success',
        description: `Deleted ${orderIds.length} orders`,
      });
    } catch (error) {
      console.error('Error deleting orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete selected orders',
        variant: 'destructive',
      });
    } finally {
      setIsBatchDeleting(false);
    }
  };

  return (
    <>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Admin Orders</h3>
        <div className="flex gap-2">
          {selectedCount > 0 && (
            <>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="outline"
                    disabled={isBatchCanceling}
                    className="border-orange-500 text-orange-600 hover:bg-orange-50"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Cancel Selected ({selectedCount})
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Cancel Selected Orders</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to cancel {selectedCount} selected
                      order{selectedCount > 1 ? 's' : ''}? This action cannot be
                      undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel disabled={isBatchCanceling}>
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleBatchCancel}
                      disabled={isBatchCanceling}
                      className="bg-orange-600 hover:bg-orange-700"
                    >
                      {isBatchCanceling ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Cancelling...
                        </>
                      ) : (
                        'Cancel Orders'
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" disabled={isBatchDeleting}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Selected ({selectedCount})
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Selected Orders</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete {selectedCount} selected
                      order{selectedCount > 1 ? 's' : ''}? This action cannot be
                      undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel disabled={isBatchDeleting}>
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleBatchDelete}
                      disabled={isBatchDeleting}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      {isBatchDeleting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Deleting...
                        </>
                      ) : (
                        'Delete Orders'
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </>
          )}

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" disabled={isDeleting}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Active Orders
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>
                  Delete All Active Admin Orders
                </AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete all active orders where either
                  the buyer or seller is an admin? This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDeleteActiveOrders}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Delete All Active Orders
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          {isDevelopment && (
            <Button
              variant="outline"
              onClick={() => setIsIncreaseBalancesModalOpen(true)}
            >
              <DollarSign className="h-4 w-4 mr-2" />
              Increase User Balances
            </Button>
          )}

          <Button onClick={() => setIsCreateModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Admin Orders
          </Button>
        </div>
      </div>

      <AdminOrdersBatchCreateModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleBatchCreateSuccess}
      />

      <IncreaseUserBalancesModal
        isOpen={isIncreaseBalancesModalOpen}
        onClose={() => setIsIncreaseBalancesModalOpen(false)}
      />
    </>
  );
}

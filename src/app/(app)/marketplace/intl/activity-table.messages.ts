import { defineMessages } from 'react-intl';

export const activityTableMessages = defineMessages({
  noActivityFound: {
    id: 'marketplace.activity.noActivityFound',
    defaultMessage: 'No activity found',
  },
  executedOrdersDescription: {
    id: 'marketplace.activity.executedOrdersDescription',
    defaultMessage: 'Executed orders will be displayed here',
  },
  orderNumber: {
    id: 'marketplace.activity.orderNumber',
    defaultMessage: 'Order #{number}',
  },
  viewOrder: {
    id: 'marketplace.activity.viewOrder',
    defaultMessage: 'View order',
  },
});

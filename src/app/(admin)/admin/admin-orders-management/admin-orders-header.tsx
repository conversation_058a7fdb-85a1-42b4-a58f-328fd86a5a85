import { DollarSign, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';

import { deleteAllActiveAdminOrders } from '@/api/admin-api';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

import { AdminOrdersBatchCreateModal } from './admin-orders-batch-create-modal';
import { IncreaseUserBalancesModal } from './increase-user-balances-modal';

interface AdminOrdersHeaderProps {
  onRefresh: () => Promise<void>;
}

export function AdminOrdersHeader({ onRefresh }: AdminOrdersHeaderProps) {
  const { toast } = useToast();
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isIncreaseBalancesModalOpen, setIsIncreaseBalancesModalOpen] =
    useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const isDevelopment = process.env.NODE_ENV === 'development';

  const handleBatchCreateSuccess = async () => {
    setIsCreateModalOpen(false);
    await onRefresh();
  };

  const handleDeleteActiveOrders = async () => {
    try {
      setIsDeleting(true);
      const deletedCount = await deleteAllActiveAdminOrders();

      toast({
        title: 'Success',
        description: `Deleted ${deletedCount} active admin orders`,
      });

      await onRefresh();
    } catch (error) {
      console.error('Error deleting active admin orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete active admin orders',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Admin Orders</h3>
        <div className="flex gap-2">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" disabled={isDeleting}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Active Orders
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>
                  Delete All Active Admin Orders
                </AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete all active orders where either
                  the buyer or seller is an admin? This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDeleteActiveOrders}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Delete All Active Orders
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          {isDevelopment && (
            <Button
              variant="outline"
              onClick={() => setIsIncreaseBalancesModalOpen(true)}
            >
              <DollarSign className="h-4 w-4 mr-2" />
              Increase User Balances
            </Button>
          )}

          <Button onClick={() => setIsCreateModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Admin Orders
          </Button>
        </div>
      </div>

      <AdminOrdersBatchCreateModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleBatchCreateSuccess}
      />

      <IncreaseUserBalancesModal
        isOpen={isIncreaseBalancesModalOpen}
        onClose={() => setIsIncreaseBalancesModalOpen(false)}
      />
    </>
  );
}

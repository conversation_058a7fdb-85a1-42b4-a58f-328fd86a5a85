import { defineMessages } from 'react-intl';

export const collectionSelectMessages = defineMessages({
  collection: {
    id: 'collectionSelect.collection',
    defaultMessage: 'Collection',
  },
  selectCollection: {
    id: 'collectionSelect.selectCollection',
    defaultMessage: 'Select collection...',
  },
  searchCollections: {
    id: 'collectionSelect.searchCollections',
    defaultMessage: 'Search collections...',
  },
  noCollectionsFound: {
    id: 'collectionSelect.noCollectionsFound',
    defaultMessage: 'No collections found matching "{searchQuery}".',
  },
});

import { getHttpEndpoint } from "@orbs-network/ton-access";
import * as admin from "firebase-admin";
import fetch from "node-fetch";
import { getMarketplaceWallet, isDevelopment } from "./config";
import { MIN_TRANSACTION_THRESHOLD_TON } from "./constants";
import { addFundsWithHistory } from "./services/balance-service";
import { applyDepositFee } from "./services/fee-service";
import { getTxLookup, updateTxLookup } from "./tx-lookup";
import { UserEntity, TxType } from "./types";
import { extractRawTonAddress, roundToThreeDecimals } from "./utils";
import { log } from "./utils/logger";

async function findUserByTonWalletFlexible(
  tonWalletAddress: string
): Promise<UserEntity | null> {
  const db = admin.firestore();
  const usersRef = db.collection("users");

  log.debug("Looking for user with TON address", {
    tonWalletAddress,
    operation: "user_lookup",
  });

  // First try exact match
  let query = usersRef.where("ton_wallet_address", "==", tonWalletAddress);
  let snapshot = await query.get();

  if (!snapshot.empty) {
    const doc = snapshot.docs[0];
    log.debug("Found exact match for address", {
      tonWalletAddress,
      userId: doc.id,
      operation: "user_lookup",
    });
    return {
      id: doc.id,
      ...doc.data(),
    } as UserEntity;
  }

  const rawAddress = extractRawTonAddress(tonWalletAddress);
  if (!rawAddress) {
    log.warn("Invalid address format", {
      tonWalletAddress,
      operation: "user_lookup",
    });
    return null;
  }

  log.debug("No exact match found, searching by raw address part", {
    tonWalletAddress,
    rawAddress,
    operation: "user_lookup",
  });

  // Query directly by raw_ton_wallet_address field - much more efficient!
  const rawQuery = usersRef.where("raw_ton_wallet_address", "==", rawAddress);
  const rawSnapshot = await rawQuery.get();

  if (!rawSnapshot.empty) {
    const doc = rawSnapshot.docs[0];
    const userData = doc.data() as UserEntity;
    log.debug("Found user by raw address", {
      userTonWallet: userData.ton_wallet_address,
      searchedAddress: tonWalletAddress,
      userId: doc.id,
      operation: "user_lookup",
    });
    return {
      ...userData,
      id: doc.id,
    } as UserEntity;
  }

  log.debug("No user found for address", {
    tonWalletAddress,
    operation: "user_lookup",
  });
  return null;
}

function filterNewTransactions(params: {
  transactions: TonTransaction[];
  lastCheckedLt: string;
}): TonTransaction[] {
  const { transactions, lastCheckedLt } = params;
  if (lastCheckedLt === "0") {
    log.monitorLog(
      "No previous transactions processed, returning all transactions",
      { monitor: "ton_transaction", count: transactions.length }
    );
    return transactions;
  }

  const filtered = transactions.filter((tx) => {
    const txLt = parseInt(tx.transaction_id.lt);
    const lastLt = parseInt(lastCheckedLt);
    // Use >= to include transactions with LT greater than OR EQUAL to lastCheckedLt
    // But we need to exclude the exact transaction that was already processed
    const isNew = txLt > lastLt;
    log.debug("Transaction filtering check", {
      transactionLt: txLt,
      lastCheckedLt: lastLt,
      isNew,
      operation: "transaction_filtering",
    });
    return isNew;
  });

  log.monitorLog("Filtered transactions down to new ones", {
    monitor: "ton_transaction",
    totalTransactions: transactions.length,
    newTransactions: filtered.length,
  });
  return filtered;
}

interface TonTransaction {
  transaction_id: {
    lt: string;
    hash?: string;
  };
  address: {
    account_address: string;
  };
  utime: number;
  in_msg?: {
    source?: string;
    value: string;
    msg_data?: {
      message?: string;
    };
  };
  out_msgs?: Array<{
    destination?: string;
    value: string;
  }>;
}

async function fetchTonTransactions(params: {
  address: string;
  fromLt?: string;
  limit?: number;
}) {
  const { address, fromLt, limit = 100 } = params;
  const network = isDevelopment() ? "testnet" : "mainnet";
  const endpoint = await getHttpEndpoint({ network });

  // Use JSON-RPC format for TonCenter API v2
  const requestBody = {
    id: "1",
    jsonrpc: "2.0",
    method: "getTransactions",
    params: {
      address: address,
      limit: limit,
      archival: true,
      ...(fromLt && fromLt !== "0" ? { to_lt: fromLt } : { to_lt: "0" }),
    },
  };

  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    Accept: "application/json",
  };

  try {
    log.info("Fetching TON transactions", {
      endpoint,
      limit,
      network,
      operation: "ton_api_call",
    });
    log.info("TON API request body", {
      requestBody,
      operation: "ton_api_call",
    });

    const response = await fetch(endpoint, {
      method: "POST",
      headers,
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();

      throw new Error(
        `TON API error: ${response.status} ${response.statusText} - ${errorText}`
      );
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(`TON API returned error: ${JSON.stringify(data.error)}`);
    }

    if (!data.ok || !data.result) {
      throw new Error(
        `TON API returned unsuccessful response: ${JSON.stringify(data)}`
      );
    }

    log.debug("Fetched TON transactions", {
      count: data.result?.length ?? 0,
      operation: "ton_api_call",
    });
    return data.result ?? [];
  } catch (error) {
    log.error("Error fetching TON transactions", error, {
      operation: "ton_api_call",
    });
    throw error;
  }
}

function extractTransactionInfo(tx: TonTransaction) {
  if (!tx?.in_msg?.source || !tx.in_msg.value) {
    return null;
  }

  const amount = roundToThreeDecimals(parseInt(tx.in_msg.value) / 1000000000);
  const originalSender = tx.in_msg.source;

  log.debug("Extracting transaction info", {
    sender: originalSender,
    amount,
    transactionId: tx.transaction_id.lt,
    operation: "transaction_extraction",
  });

  return {
    sender: originalSender, // Keep original address, flexible matching will handle it
    amount,
    message: tx.in_msg.msg_data?.message,
  };
}

async function updateUserBalance(params: { userId: string; amount: number }) {
  const { userId, amount } = params;
  try {
    const netAmount = await applyDepositFee({ depositAmount: amount });

    await addFundsWithHistory({
      userId,
      amount: netAmount,
      txType: TxType.DEPOSIT,
      description: `Deposit from TON wallet (original: ${amount} TON, after fees: ${netAmount} TON)`,
    });
    log.balanceLog("Updated balance for user after deposit", {
      userId,
      amount: netAmount,
      originalAmount: amount,
      operation: "deposit_processing",
    });
  } catch (error) {
    log.error("Error updating balance for user", error, {
      userId,
      operation: "deposit_processing",
    });
    throw error;
  }
}

async function processTransactions(transactions: TonTransaction[]) {
  log.monitorLog("Processing transactions", {
    monitor: "ton_transaction",
    count: transactions.length,
    status: "processing_started",
  });

  for (const tx of transactions) {
    try {
      const txInfo = extractTransactionInfo(tx);

      if (!txInfo) {
        continue;
      }

      log.transactionLog("Processing transaction", {
        transactionId: tx.transaction_id.lt,
        sender: txInfo.sender,
        amount: txInfo.amount,
      });

      const user = await findUserByTonWalletFlexible(txInfo.sender);

      if (!user) {
        log.warn("No user found for wallet address", {
          walletAddress: txInfo.sender,
          transactionId: tx.transaction_id.lt,
          operation: "transaction_processing",
        });
        continue;
      }

      await updateUserBalance({ userId: user.id, amount: txInfo.amount });

      log.transactionLog("Successfully processed topup", {
        transactionId: tx.transaction_id.lt,
        userId: user.id,
        userTgId: user.tg_id,
        amount: txInfo.amount,
      });
    } catch (error) {
      log.error("Error processing transaction", error, {
        transactionId: tx.transaction_id.lt,
        operation: "transaction_processing",
      });
    }
  }
}

export async function monitorTonTransactions() {
  try {
    log.monitorLog("Starting TON transaction monitoring", {
      monitor: "ton_transaction",
      status: "started",
    });

    const txLookup = await getTxLookup();
    const lastCheckedLt = txLookup?.last_checked_record_id ?? "0";

    log.monitorLog("Retrieved last checked LT", {
      monitor: "ton_transaction",
      lastCheckedLt,
    });

    const marketplaceWallet = getMarketplaceWallet();

    // Fetch transactions in smaller batches to reduce API load
    // Start with a reasonable limit to avoid fetching too many small transactions
    const batchLimit = 50;
    const allTransactions = await fetchTonTransactions({
      address: marketplaceWallet,
      fromLt: lastCheckedLt !== "0" ? lastCheckedLt : undefined,
      limit: batchLimit,
    });

    if (allTransactions.length === 0) {
      log.monitorLog("No transactions found", {
        monitor: "ton_transaction",
        status: "no_transactions",
      });
      return;
    }

    // Filter out already processed transactions first
    const newTransactions = filterNewTransactions({
      transactions: allTransactions,
      lastCheckedLt,
    });

    if (newTransactions.length === 0) {
      log.monitorLog("No new transactions to process", {
        monitor: "ton_transaction",
        status: "no_new_transactions",
      });
      return;
    }

    log.monitorLog("Found new transactions to process", {
      monitor: "ton_transaction",
      count: newTransactions.length,
    });

    // Pre-filter transactions by amount > minimum threshold before processing
    const significantTransactions = newTransactions.filter((tx) => {
      if (!tx?.in_msg?.value) return false;
      const amount = parseInt(tx.in_msg.value) / 1000000000;
      return amount > MIN_TRANSACTION_THRESHOLD_TON;
    });

    log.monitorLog("Filtered to significant transactions", {
      monitor: "ton_transaction",
      significantCount: significantTransactions.length,
      minThreshold: MIN_TRANSACTION_THRESHOLD_TON,
    });

    if (significantTransactions.length === 0) {
      log.monitorLog("No significant transactions to process", {
        monitor: "ton_transaction",
        status: "no_significant_transactions",
      });
      // Still update the LT to mark these transactions as processed
      if (newTransactions.length > 0) {
        // Find the transaction with the highest LT (most recent)
        const latestLt = Math.max(
          ...newTransactions.map((tx) => parseInt(tx.transaction_id.lt))
        ).toString();
        await updateTxLookup(latestLt);
        log.monitorLog(
          "Updated last checked LT (no significant transactions processed)",
          {
            monitor: "ton_transaction",
            latestLt,
            status: "lt_updated",
          }
        );
      }
      return;
    }

    // Sort transactions by LT (oldest first)
    significantTransactions.sort(
      (a, b) => parseInt(a.transaction_id.lt) - parseInt(b.transaction_id.lt)
    );

    await processTransactions(significantTransactions);

    // Update LT based on all new transactions (not just significant ones)
    if (newTransactions.length > 0) {
      // Find the transaction with the highest LT (most recent)
      const latestLt = Math.max(
        ...newTransactions.map((tx) => parseInt(tx.transaction_id.lt))
      ).toString();
      await updateTxLookup(latestLt);
      log.monitorLog("Updated last checked LT", {
        monitor: "ton_transaction",
        latestLt,
        status: "lt_updated",
      });
    }

    log.monitorLog("TON transaction monitoring completed successfully", {
      monitor: "ton_transaction",
      status: "completed",
    });
  } catch (error) {
    log.error("Error in TON transaction monitoring", error, {
      monitor: "ton_transaction",
      status: "error",
    });
    throw error;
  }
}

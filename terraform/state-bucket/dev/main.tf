terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.28.0"
    }
  }
}

provider "google" {
  project = "marketplace-dev-76a4a"
  region  = "us-central1"
}

resource "google_storage_bucket" "terraform_state" {
  name          = "marketplace-dev-76a4a-terraform-state"
  location      = "US"
  force_destroy = false

  versioning {
    enabled = true
  }

  uniform_bucket_level_access = true

  lifecycle_rule {
    condition {
      age = 3650  # 10 year for dev
    }
    action {
      type = "Delete"
    }
  }

  lifecycle {
    prevent_destroy = true
  }
}

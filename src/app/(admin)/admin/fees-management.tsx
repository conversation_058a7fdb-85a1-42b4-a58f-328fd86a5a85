'use client';

import { DollarSign, Loader2 } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Form } from '@/components/ui/form';

import { FeesFormField } from './fees-management/fees-form-field';
import { feeFieldConfigs } from './fees-management/fees-management-types';
import { getValidationWarnings } from './fees-management/fees-management-utils';
import { FeesSummaryDisplay } from './fees-management/fees-summary-display';
import { FeesValidationWarnings } from './fees-management/fees-validation-warnings';
import { useFeesManagement } from './fees-management/use-fees-management';

export const FeesManagement = () => {
  const { form, isLoading, isLoadingData, onSubmit } = useFeesManagement();
  const validationWarnings = getValidationWarnings(form.watch());

  if (isLoadingData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Fees Management
          </CardTitle>
          <CardDescription>
            Configure marketplace fees and referral rates
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading fees configuration...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <DollarSign className="h-4 w-4" />
          Fees Management
        </CardTitle>
        <CardDescription className="text-sm">
          Configure marketplace fees. Deposit/Withdraw are static TON values.
          Lock percentages are 0.0-1.0. Others are BPS (100 BPS = 1%).
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <FeesValidationWarnings warnings={validationWarnings} />
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {feeFieldConfigs.map((config) => (
                <FeesFormField
                  key={config.name}
                  control={form.control}
                  config={config}
                />
              ))}
            </div>

            <FeesSummaryDisplay watch={form.watch} />

            <Button type="submit" disabled={isLoading} className="w-full">
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating Fees...
                </>
              ) : (
                'Update Fees Configuration'
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

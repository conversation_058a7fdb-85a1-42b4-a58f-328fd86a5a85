import { defineMessages } from 'react-intl';

export const marketplaceFiltersMessages = defineMessages({
  min: {
    id: 'marketplaceFilters.min',
    defaultMessage: 'Min',
  },
  max: {
    id: 'marketplaceFilters.max',
    defaultMessage: 'Max',
  },
  allCollections: {
    id: 'marketplaceFilters.allCollections',
    defaultMessage: 'All Collections',
  },
  sortBy: {
    id: 'marketplaceFilters.sortBy',
    defaultMessage: 'Sort by',
  },
  newestFirst: {
    id: 'marketplaceFilters.newestFirst',
    defaultMessage: 'Newest First',
  },
  oldestFirst: {
    id: 'marketplaceFilters.oldestFirst',
    defaultMessage: 'Oldest First',
  },
  priceHighToLow: {
    id: 'marketplaceFilters.priceHighToLow',
    defaultMessage: 'Price: High to Low',
  },
  priceLowToHigh: {
    id: 'marketplaceFilters.priceLowToHigh',
    defaultMessage: 'Price: Low to High',
  },
});

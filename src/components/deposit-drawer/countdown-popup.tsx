'use client';

import { CheckCircle, Clock, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';

import { countdownPopupMessages } from './intl/countdown-popup.messages';

interface CountdownPopupProps {
  show: boolean;
  onClose: () => void;
  onComplete: () => void;
  initialSeconds?: number;
  title?: string;
  message?: string;
  onInterval?: (currentTime: number) => void;
}

export function CountdownPopup({
  show,
  onClose,
  onComplete,
  initialSeconds = 60,
  title,
  message,
  onInterval,
}: CountdownPopupProps) {
  const { formatMessage: t } = useIntl();
  const [countdown, setCountdown] = useState(initialSeconds);

  const defaultTitle = title || t(countdownPopupMessages.depositProcessing);
  const defaultMessage =
    message || t(countdownPopupMessages.youWillReceiveFundsWithin);

  useEffect(() => {
    if (show) {
      setCountdown(initialSeconds);
    }
  }, [show, initialSeconds]);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (show && countdown > 0) {
      interval = setInterval(() => {
        setCountdown((prev) => {
          const newTime = prev - 1;
          onInterval?.(newTime);
          return newTime;
        });
      }, 1000);
    } else if (countdown === 0 && show) {
      onComplete();
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [show, countdown, onComplete, onInterval]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!show) return null;

  return (
    <div className="fixed bottom-18 left-4 right-4 max-w-sm mx-auto">
      <div className="bg-[#17212b] border border-[#3a4a5c]/30 rounded-lg shadow-lg p-4 animate-in slide-in-from-bottom-2 duration-300">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            <CheckCircle className="w-6 h-6 text-green-400" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-sm font-medium text-[#f5f5f5]">
                {defaultTitle}
              </h3>
              <div className="flex items-center gap-1 text-xs text-[#708499]">
                <Clock className="w-3 h-3" />
              </div>
            </div>
            <p className="text-sm text-[#708499]">
              {defaultMessage} {formatTime(countdown)}{' '}
              {t(countdownPopupMessages.minutes)}.
            </p>
          </div>
          <button
            onClick={onClose}
            className="flex-shrink-0 text-[#708499] hover:text-[#f5f5f5] transition-colors"
            aria-label={t(countdownPopupMessages.closeNotification)}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}

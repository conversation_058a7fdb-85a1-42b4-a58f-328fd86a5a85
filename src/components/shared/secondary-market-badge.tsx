import { useIntl } from 'react-intl';

import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

import { secondaryMarketBadgeMessages } from './secondary-market-badge/intl/secondary-market-badge.messages';

interface SecondaryMarketBadgeProps {
  className?: string;
}

export function SecondaryMarketBadge({ className }: SecondaryMarketBadgeProps) {
  const { formatMessage: t } = useIntl();

  return (
    <Badge
      variant="secondary"
      className={cn(
        'bg-[#6ab2f2]/20 text-[#6ab2f2] border-[#6ab2f2]/30 text-[10px]',
        className,
      )}
    >
      {t(secondaryMarketBadgeMessages.resell)}
    </Badge>
  );
}

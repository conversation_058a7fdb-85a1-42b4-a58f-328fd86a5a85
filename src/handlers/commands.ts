import { Context } from "telegraf";
import {
  createMainKeyboard,
  createMarketplaceInlineKeyboard,
} from "../utils/keyboards";
import { MESSAGES } from "../constants/messages";
import { HealthcheckService } from "../services/healthcheck";
import { log } from "../utils/logger";

export const handleStartCommand = (ctx: Context) => {
  ctx.reply(MESSAGES.WELCOME, createMainKeyboard());
};

export const handleHelpCommand = (ctx: Context) => {
  ctx.reply(MESSAGES.HELP, createMarketplaceInlineKeyboard());
};

export const handleHealthCommand = async (ctx: Context) => {
  try {
    const lastHealthcheck = await HealthcheckService.getLastHealthcheck();
    const isHealthy = await HealthcheckService.isHealthy();

    if (lastHealthcheck) {
      const healthStatus = isHealthy ? "✅ Healthy" : "⚠️ Unhealthy";
      const message = `${healthStatus}\n\nLast healthcheck: ${lastHealthcheck}`;
      ctx.reply(message);
    } else {
      ctx.reply("❌ No healthcheck data found");
    }
  } catch (error) {
    log.error("Error in health command", error, {
      operation: "health_command",
      chatId: String(ctx.chat?.id),
      userId: String(ctx.from?.id),
    });
    ctx.reply("❌ Error checking health status");
  }
};

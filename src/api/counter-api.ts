import { doc, runTransaction, serverTimestamp } from 'firebase/firestore';

import { COUNTERS_COLLECTION_NAME } from '@/core.constants';
import { firestore } from '@/root-context';

export interface CounterEntity {
  id: string;
  value: number;
  updatedAt: Date;
}

export async function getNextCounterValue(
  counterName: string,
  isAdminUser = false,
): Promise<number> {
  const counterRef = doc(firestore, COUNTERS_COLLECTION_NAME, counterName);

  try {
    const nextValue = await runTransaction(firestore, async (transaction) => {
      const counterDoc = await transaction.get(counterRef);

      let currentValue = 0;
      if (counterDoc.exists()) {
        const counterData = counterDoc.data() as CounterEntity;
        currentValue = counterData.value;
      }

      const increment = isAdminUser
        ? Math.floor(Math.random() * 51) + 50 // Random 50-100 for admins
        : 1; // Normal increment for regular users

      const nextValue = currentValue + increment;

      transaction.set(
        counterRef,
        {
          id: counterName,
          value: nextValue,
          updatedAt: serverTimestamp(),
        },
        { merge: true },
      );

      return nextValue;
    });

    return nextValue;
  } catch (error) {
    console.error(
      `Error getting next counter value for ${counterName}:`,
      error,
    );
    throw error;
  }
}

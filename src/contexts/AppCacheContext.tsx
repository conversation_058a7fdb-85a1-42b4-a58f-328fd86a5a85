'use client';

import type { DocumentSnapshot } from 'firebase/firestore';
import { createContext, type ReactNode, useContext, useMemo } from 'react';

import { CachePatterns } from '@/core.constants';

export interface CacheConfig {
  duration: number;
  maxSize?: number;
}

export interface PaginatedCacheData<T> {
  pages: T[][];
  lastDocs: (DocumentSnapshot | null)[];
  hasMore: boolean;
  timestamp: number;
  totalFetched: number;
}

export interface SimpleCacheData<T> {
  data: T;
  timestamp: number;
}

export interface CdnAvailabilityData {
  available: boolean;
  timestamp: number;
}

export type CacheEntry<T> =
  | SimpleCacheData<T>
  | PaginatedCacheData<T>
  | CdnAvailabilityData;

export interface CacheManager {
  get<T>(key: string): T | null;
  set<T>(key: string, data: T, config?: CacheConfig): void;

  getPaginatedResult<T>(
    key: string,
    pageIndex?: number,
  ): { data: T[]; lastDoc: DocumentSnapshot | null; hasMore: boolean } | null;
  setPaginatedResult<T>(
    key: string,
    data: T[],
    lastDoc: DocumentSnapshot | null,
    hasMore: boolean,
    config?: CacheConfig,
  ): void;
  appendPaginatedPage<T>(
    key: string,
    data: T[],
    lastDoc: DocumentSnapshot | null,
    hasMore: boolean,
  ): void;

  getCdnAvailability(url: string): boolean | null;
  setCdnAvailability(
    url: string,
    available: boolean,
    config?: CacheConfig,
  ): void;

  invalidate(key: string): void;
  invalidatePattern(pattern: string): void;
  clear(): void;
  clearExpired(): void;

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  generateKey(prefix: string, params: Record<string, any>): string;

  getCollections<T>(): T[] | null;
  setCollections<T>(collections: T[], config?: CacheConfig): void;

  getAppConfig<T>(): T | null;
  setAppConfig<T>(config: T, configOptions?: CacheConfig): void;

  getFeesConfig<T>(): T | null;
  setFeesConfig<T>(config: T, configOptions?: CacheConfig): void;
}

class AppCacheManager implements CacheManager {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private readonly cache = new Map<string, CacheEntry<any>>();
  private readonly defaultConfig: CacheConfig = {
    duration: 5 * 60 * 1000, // 5 minutes
    maxSize: 1000,
  };

  private isExpired(timestamp: number, duration: number): boolean {
    return Date.now() - timestamp > duration;
  }

  private enforceMaxSize(): void {
    if (
      this.defaultConfig.maxSize &&
      this.cache.size > this.defaultConfig.maxSize
    ) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

      const toRemove = this.cache.size - this.defaultConfig.maxSize + 1;
      for (let i = 0; i < toRemove; i++) {
        this.cache.delete(entries[i][0]);
      }
    }
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key) as SimpleCacheData<T> | undefined;

    if (!entry) return null;

    if (this.isExpired(entry.timestamp, this.defaultConfig.duration)) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  set<T>(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    } as SimpleCacheData<T>);

    this.enforceMaxSize();
  }

  getPaginatedResult<T>(
    key: string,
    pageIndex: number = 0,
  ): { data: T[]; lastDoc: DocumentSnapshot | null; hasMore: boolean } | null {
    const entry = this.cache.get(key) as PaginatedCacheData<T> | undefined;

    if (!entry || !('pages' in entry)) return null;

    if (this.isExpired(entry.timestamp, this.defaultConfig.duration)) {
      this.cache.delete(key);
      return null;
    }

    if (pageIndex >= entry.pages.length) return null;

    const hasMore = pageIndex + 1 < entry.pages.length || entry.hasMore;

    return {
      data: entry.pages[pageIndex],
      lastDoc: entry.lastDocs[pageIndex],
      hasMore,
    };
  }

  setPaginatedResult<T>(
    key: string,
    data: T[],
    lastDoc: DocumentSnapshot | null,
    hasMore: boolean,
  ): void {
    this.cache.set(key, {
      pages: [data],
      lastDocs: [lastDoc],
      hasMore,
      timestamp: Date.now(),
      totalFetched: data.length,
    } as PaginatedCacheData<T>);

    this.enforceMaxSize();
  }

  appendPaginatedPage<T>(
    key: string,
    data: T[],
    lastDoc: DocumentSnapshot | null,
    hasMore: boolean,
  ): void {
    const entry = this.cache.get(key) as PaginatedCacheData<T> | undefined;

    if (!entry || !('pages' in entry)) {
      this.setPaginatedResult(key, data, lastDoc, hasMore);
      return;
    }

    entry.pages.push(data);
    entry.lastDocs.push(lastDoc);
    entry.hasMore = hasMore;
    entry.totalFetched += data.length;
    entry.timestamp = Date.now();
  }

  getCdnAvailability(url: string): boolean | null {
    const key = `cdn:${url}`;
    const entry = this.cache.get(key) as CdnAvailabilityData | undefined;

    if (!entry || !('available' in entry)) return null;

    if (this.isExpired(entry.timestamp, this.defaultConfig.duration)) {
      this.cache.delete(key);
      return null;
    }

    return entry.available;
  }

  setCdnAvailability(url: string, available: boolean): void {
    const key = `cdn:${url}`;

    this.cache.set(key, {
      available,
      timestamp: Date.now(),
    } as CdnAvailabilityData);

    this.enforceMaxSize();
  }

  invalidate(key: string): void {
    this.cache.delete(key);
  }

  invalidatePattern(pattern: string): void {
    const regex = new RegExp(pattern);
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  clear(): void {
    this.cache.clear();
  }

  clearExpired(): void {
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry.timestamp, this.defaultConfig.duration)) {
        this.cache.delete(key);
      }
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  generateKey(prefix: string, params: Record<string, any>): string {
    try {
      const safeParams = Object.fromEntries(
        Object.entries(params).filter(([, value]) => value !== undefined),
      );
      return `${prefix}:${JSON.stringify(safeParams)}`;
    } catch (error) {
      console.error('Error generating cache key:', error);
      return `${prefix}:fallback:${Date.now()}`;
    }
  }

  getCollections<T>(): T[] | null {
    return this.get<T[]>(`${CachePatterns.COLLECTIONS}:all`);
  }

  setCollections<T>(collections: T[]): void {
    this.set(`${CachePatterns.COLLECTIONS}:all`, collections);
  }

  getAppConfig<T>(): T | null {
    return this.get<T>(`${CachePatterns.APP_CONFIG}:main`);
  }

  setAppConfig<T>(config: T): void {
    this.set(`${CachePatterns.APP_CONFIG}:main`, config);
  }

  getFeesConfig<T>(): T | null {
    return this.get<T>('app_config:fees');
  }

  setFeesConfig<T>(config: T): void {
    this.set('app_config:fees', config);
  }
}

const AppCacheContext = createContext<CacheManager | null>(null);

export const useAppCache = (): CacheManager => {
  const context = useContext(AppCacheContext);
  if (!context) {
    throw new Error('useAppCache must be used within an AppCacheProvider');
  }
  return context;
};

interface AppCacheProviderProps {
  children: ReactNode;
}

export const AppCacheProvider = ({ children }: AppCacheProviderProps) => {
  const cacheManager = useMemo(() => new AppCacheManager(), []);

  return (
    <AppCacheContext.Provider value={cacheManager}>
      {children}
    </AppCacheContext.Provider>
  );
};

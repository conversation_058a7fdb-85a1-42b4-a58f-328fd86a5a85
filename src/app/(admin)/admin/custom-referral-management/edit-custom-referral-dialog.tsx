import { zodResolver } from '@hookform/resolvers/zod';
import { AlertTriangle } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { updateUser } from '@/api/user-api';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';

import {
  type EditReferralFormData,
  editReferralSchema,
  type UserWithCustomReferral,
} from './custom-referral-types';
import { getCustomReferralWarnings } from './custom-referral-utils';

interface EditCustomReferralDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  user: UserWithCustomReferral | null;
  maxPurchaseFee: number;
  maxResellPurchaseFee: number;
}

export const EditCustomReferralDialog = ({
  isOpen,
  onClose,
  onSuccess,
  user,
  maxPurchaseFee,
  maxResellPurchaseFee,
}: EditCustomReferralDialogProps) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<EditReferralFormData>({
    resolver: zodResolver(editReferralSchema),
    defaultValues: {
      referralFee: 0,
    },
  });

  const referralFee = form.watch('referralFee');
  const warnings = getCustomReferralWarnings(
    referralFee,
    maxPurchaseFee,
    maxResellPurchaseFee,
  );

  useEffect(() => {
    if (user) {
      form.reset({
        referralFee: user.referral_fee,
      });
    }
  }, [user, form]);

  const onSubmit = async (data: EditReferralFormData) => {
    if (!user) return;

    try {
      setIsLoading(true);
      await updateUser(user.id, {
        referral_fee: data.referralFee,
      });

      toast({
        title: 'Success',
        description: 'Custom referral fee updated successfully',
      });

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error updating custom referral fee:', error);
      toast({
        title: 'Error',
        description: 'Failed to update custom referral fee',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Custom Referral Fee</DialogTitle>
          <DialogDescription>
            Update the custom referral fee for{' '}
            {user?.displayName || 'this user'}.
          </DialogDescription>
        </DialogHeader>

        {warnings.length > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Configuration Warnings</AlertTitle>
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1">
                {warnings.map((warning) => (
                  <li key={warning} className="text-sm">
                    {warning}
                  </li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="referralFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Referral Fee (BPS)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="200"
                      {...field}
                      onChange={(e) =>
                        field.onChange(Number(e.target.value) || 0)
                      }
                    />
                  </FormControl>
                  <FormDescription>
                    Custom referral fee in basis points (100 BPS = 1%)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Updating...' : 'Update Fee'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

'use client';

import { TabsList } from '@telegram-apps/telegram-ui';
import { TabsItem } from '@telegram-apps/telegram-ui/dist/components/Navigation/TabsList/components/TabsItem/TabsItem';
import { useIntl } from 'react-intl';

import { marketplaceTabsMessages } from './intl/marketplace-tabs.messages';
import type { TabType } from './use-marketplace-orders';

interface MarketplaceTabsProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
}

export const MarketplaceTabs = ({
  activeTab,
  onTabChange,
}: MarketplaceTabsProps) => {
  const { formatMessage: t } = useIntl();

  return (
    <TabsList className="grid w-full grid-cols-3 gap-0!">
      <TabsItem
        selected={activeTab === 'buyers'}
        onClick={() => onTabChange('buyers')}
      >
        {t(marketplaceTabsMessages.buy)}
      </TabsItem>
      <TabsItem
        selected={activeTab === 'sellers'}
        onClick={() => onTabChange('sellers')}
      >
        {t(marketplaceTabsMessages.sell)}
      </TabsItem>
      <TabsItem
        selected={activeTab === 'activity'}
        onClick={() => onTabChange('activity')}
      >
        {t(marketplaceTabsMessages.activity)}
      </TabsItem>
    </TabsList>
  );
};

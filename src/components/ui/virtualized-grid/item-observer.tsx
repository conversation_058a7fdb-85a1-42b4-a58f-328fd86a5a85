'use client';

import { useEffect, useRef } from 'react';

import { DEFAULT_HEIGHT, useItemCacheContext } from './item-cache-context';

interface ItemObserverProps {
  children: React.ReactNode;
  itemId: string;
  initialIsIntersecting?: boolean;
  className?: string;
}

export const ItemObserver = ({
  children,
  itemId,
  initialIsIntersecting = false,
  className = '',
}: ItemObserverProps) => {
  const intersectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const {
    loadIntersectionObserver,
    resizeObserver,
    loadIntersecting = initialIsIntersecting,
    wrapperHeight = DEFAULT_HEIGHT,
    fixed,
  } = useItemCacheContext(itemId);

  useEffect(() => {
    const intersectionNode = intersectionRef.current;
    const resizeNode = contentRef.current;

    if (intersectionNode && resizeNode) {
      loadIntersectionObserver.observe(intersectionNode);
      resizeObserver.observe(resizeNode);

      return () => {
        loadIntersectionObserver.unobserve(intersectionNode);
        resizeObserver.unobserve(resizeNode);
      };
    }
  }, [loadIntersectionObserver, resizeObserver]);

  const shouldRender = loadIntersecting || fixed;
  const shouldUseAutoHeight =
    initialIsIntersecting && wrapperHeight === DEFAULT_HEIGHT;

  return (
    <div
      className={`item-observer-wrapper ${className}`}
      style={{
        height: shouldUseAutoHeight ? 'auto' : wrapperHeight,
        minHeight: shouldUseAutoHeight ? undefined : wrapperHeight,
      }}
      ref={intersectionRef}
      data-cache-key={itemId}
    >
      <div
        className="item-content-wrapper"
        ref={contentRef}
        data-cache-key={itemId}
        data-track="wrapper"
      >
        {shouldRender && children}
      </div>
    </div>
  );
};

import { ArrowUpRight } from 'lucide-react';
import { useIntl } from 'react-intl';

import { emptyStateMessages } from './intl/empty-state.messages';

export function EmptyState() {
  const { formatMessage: t } = useIntl();

  return (
    <div className="text-center py-12 px-4">
      <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-[#3a4a5c]/30 flex items-center justify-center">
        <ArrowUpRight className="w-8 h-8 text-[#708499]" />
      </div>
      <p className="text-[#f5f5f5] text-lg font-medium mb-2">
        {t(emptyStateMessages.noTransactionsYet)}
      </p>
      <p className="text-[#708499] text-sm max-w-md mx-auto">
        {t(emptyStateMessages.transactionHistoryDescription)}
      </p>
    </div>
  );
}

import { useCallback, useState } from 'react';

interface PagePaginationState<T> {
  items: T[];
  loading: boolean;
  currentPage: number;
  totalPages: number;
  totalItems: number;
}

interface PagePaginationOptions {
  pageSize?: number;
}

interface PagePaginationResult<T> {
  items: T[];
  hasMore: boolean;
  totalItems: number;
}

export const usePagePagination = <T>(
  fetchFunction: (
    page: number,
    pageSize: number,
  ) => Promise<PagePaginationResult<T>>,
  options: PagePaginationOptions = {},
) => {
  const { pageSize = 25 } = options;

  const [state, setState] = useState<PagePaginationState<T>>({
    items: [],
    loading: false,
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
  });

  const loadPage = useCallback(
    async (page: number) => {
      setState((prev) => ({ ...prev, loading: true }));

      try {
        const result = await fetchFunction(page, pageSize);
        const totalPages = Math.ceil(result.totalItems / pageSize);

        setState({
          items: result.items,
          loading: false,
          currentPage: page,
          totalPages,
          totalItems: result.totalItems,
        });
      } catch (error) {
        console.error('Error loading page:', error);
        setState((prev) => ({ ...prev, loading: false }));
      }
    },
    [fetchFunction, pageSize],
  );

  const goToPage = useCallback(
    (page: number) => {
      if (page >= 1 && page <= state.totalPages && page !== state.currentPage) {
        loadPage(page);
      }
    },
    [loadPage, state.totalPages, state.currentPage],
  );

  const refresh = useCallback(() => {
    loadPage(state.currentPage);
  }, [loadPage, state.currentPage]);

  return {
    items: state.items,
    loading: state.loading,
    currentPage: state.currentPage,
    totalPages: state.totalPages,
    totalItems: state.totalItems,
    goToPage,
    loadPage,
    refresh,
  };
};

import { defineMessages } from 'react-intl';

export const createOrderDrawerMessages = defineMessages({
  createSellOrder: {
    id: 'marketplace.createOrder.createSellOrder',
    defaultMessage: 'Create Sell Order',
  },
  create: {
    id: 'marketplace.createOrder.create',
    defaultMessage: 'Create',
  },
  creating: {
    id: 'marketplace.createOrder.creating',
    defaultMessage: 'Creating...',
  },
  cancel: {
    id: 'marketplace.createOrder.cancel',
    defaultMessage: 'Cancel',
  },
  price: {
    id: 'marketplace.createOrder.price',
    defaultMessage: 'Price',
  },
  enterPrice: {
    id: 'marketplace.createOrder.enterPrice',
    defaultMessage: 'Enter price in TON',
  },
  collection: {
    id: 'marketplace.createOrder.collection',
    defaultMessage: 'Collection',
  },
  selectCollection: {
    id: 'marketplace.createOrder.selectCollection',
    defaultMessage: 'Select collection...',
  },
  insufficientBalance: {
    id: 'marketplace.createOrder.insufficientBalance',
    defaultMessage: 'Insufficient balance',
  },
  minimumPrice: {
    id: 'marketplace.createOrder.minimumPrice',
    defaultMessage: 'Minimum price is {amount} TON',
  },
  maximumPrice: {
    id: 'marketplace.createOrder.maximumPrice',
    defaultMessage: 'Maximum price is {amount} TON',
  },
});

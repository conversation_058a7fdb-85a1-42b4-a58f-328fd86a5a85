import dotenv from "dotenv";
import path from "path";
import fs from "fs";
import { log } from "../utils/logger";

export function loadEnvironment() {
  const nodeEnv = process.env.NODE_ENV ?? "development";

  let envFile: string;
  if (nodeEnv === "development") {
    // In development mode, prioritize .env.local over .env.development
    const localEnvPath = path.resolve(process.cwd(), ".env.local");
    if (fs.existsSync(localEnvPath)) {
      envFile = ".env.local";
    } else {
      envFile = ".env.development";
    }
  } else if (nodeEnv === "production") {
    envFile = ".env.production";
  } else {
    envFile = ".env.development";
  }

  console.log("Current env file:", envFile);

  const envPath = path.resolve(process.cwd(), envFile);

  // Load the environment file
  const result = dotenv.config({ path: envPath });

  console.log("Current env path:", envPath);
  console.log("Current env variables:", JSON.stringify(process.env));

  if (result.error) {
    log.warn(
      `⚠️ Warning: Could not load ${envFile}. Using default environment variables.`,
      {
        operation: "env_loading",
        envFile,
        error: result.error.message,
      }
    );
  } else {
    log.info(`✅ Loaded environment from ${envFile}`, {
      operation: "env_loading",
      envFile,
    });
  }

  return result;
}

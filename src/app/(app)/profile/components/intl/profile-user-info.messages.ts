import { defineMessages } from 'react-intl';

export const profileUserInfoMessages = defineMessages({
  profileInformation: {
    id: 'profile.userInfo.profileInformation',
    defaultMessage: 'Profile Information',
  },
  anonymousUser: {
    id: 'profile.userInfo.anonymousUser',
    defaultMessage: 'Anonymous User',
  },
  availableBalance: {
    id: 'profile.userInfo.availableBalance',
    defaultMessage: 'Available Balance',
  },
  lockedBalance: {
    id: 'profile.userInfo.lockedBalance',
    defaultMessage: 'Locked Balance',
  },
  totalBalance: {
    id: 'profile.userInfo.totalBalance',
    defaultMessage: 'Total Balance',
  },
  myPoints: {
    id: 'profile.userInfo.myPoints',
    defaultMessage: 'My Points',
  },
});

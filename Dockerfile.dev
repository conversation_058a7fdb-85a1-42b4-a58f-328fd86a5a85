# Use a Node.js Alpine base image
FROM node:20-alpine

# Install dependencies for Bun and curl
RUN apk add --no-cache curl bash

# Install Bun
RUN curl -fsSL https://bun.sh/install | bash
ENV PATH="/root/.bun/bin:$PATH"

# Set working directory
WORKDIR /app

# Copy package.json and install dependencies
COPY package.json bun.lock .env.development ./
RUN bun install

# Copy the rest of the application
COPY . .

RUN bun run build

# Expose the port
EXPOSE 8080

# Start the app with Bun Dev
CMD ["bun", "run", "start:dev"]

# Firebase Cloud Functions Local Debugging Guide

## Prerequisites

- Firebase CLI installed (✅ v14.7.0 detected)
- Node.js 20+ (as specified in package.json)
- Firebase project configured

## Quick Setup

### 1. One-Command Setup (Recommended)

```bash
# From marketplace-functions directory
./setup-local-debug.sh
```

This script will:

- Install dependencies
- Set up local environment variables
- Build functions
- Start emulators

### 2. Manual Setup

#### Step 1: Install Dependencies

```bash
cd functions
npm install
```

#### Step 2: Configure Environment Variables

Create `functions/.env.local` for local development:

```bash
# Copy from the template and fill in values
cp functions/.env.example functions/.env.local
```

Edit `functions/.env.local` with your values:

```env
# Required for local development
TON_RPC_URL_MAINNET=https://misty-crimson-friday.ton-mainnet.quiknode.pro/bd21cc29f2080adf31da2fe2af76f1f6df3d7b5c
TON_MARKETPLACE_WALLET=UQCWcm7v_aEFlLEUmAYuryEfpepeRcFVSVIimfmCYmxbE7gI
TON_MARKETPLACE_WALLET_MNEMONIC="spice myth collect display ship legend seminar injury setup voice faith steel erase captain supply cave range author neither decrease hire update mechanic state"

# Telegram (optional for withdrawFunds testing)
TELEGRAM_API_HASH=da8fa0a17dd9e0c1b9e420d73a39a710

# Firebase
FIREBASE_PROJECT_ID=your-project-id
NODE_ENV=development
```

#### Step 3: Build Functions

```bash
npm run build
```

#### Step 4: Start Firebase Emulators

From the project root (`marketplace-functions`):

```bash
firebase emulators:start
```

This will start:

- Functions Emulator: http://localhost:5001
- Firestore Emulator: http://localhost:8080
- Auth Emulator: http://localhost:9099
- Storage Emulator: http://localhost:9199
- Emulator UI: http://localhost:4000

#### Alternative: Start Only Functions Emulator

```bash
firebase emulators:start --only functions
```

## Debugging withdrawFunds Function

### Method 1: Using Firebase Functions Shell

1. Start the functions shell:

```bash
cd functions
npm run shell
```

2. In the shell, call the function:

```javascript
withdrawFunds({ amount: 1.5 }, { auth: { uid: "test-user-id" } });
```

### Method 2: Using HTTP Requests

1. Start emulators:

```bash
firebase emulators:start
```

2. Make HTTP request to the function:

```bash
curl -X POST \
  http://localhost:5001/YOUR_PROJECT_ID/us-central1/withdrawFunds \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AUTH_TOKEN" \
  -d '{"data": {"amount": 1.5}}'
```

### Method 3: Using Emulator UI

1. Open http://localhost:4000
2. Navigate to Functions tab
3. Find `withdrawFunds` function
4. Use the test interface to send requests

## Debugging Tips

### 1. Enable Verbose Logging

Add to your function code:

```typescript
console.log("Debug: Function started with data:", data);
console.log("Debug: User ID:", context.auth?.uid);
```

### 2. Watch for Changes

Keep functions rebuilding automatically:

```bash
cd functions
npm run build:watch
```

In another terminal, restart emulators when code changes:

```bash
firebase emulators:start --only functions
```

### 3. View Logs

Logs appear in:

- Terminal where emulators are running
- Emulator UI at http://localhost:4000 → Functions → Logs
- Functions shell output

### 4. Debug Firestore Operations

- View Firestore data in Emulator UI: http://localhost:4000 → Firestore
- Add debug logs before/after database operations:

```typescript
console.log("Debug: Fetching user document for ID:", userId);
const userDoc = await db.collection("users").doc(userId).get();
console.log("Debug: User document exists:", userDoc.exists);
console.log("Debug: User data:", userDoc.data());
```

## Testing withdrawFunds Function

### 1. Setup Test Data

First, create test user in Firestore emulator:

```javascript
// In functions shell or via Emulator UI
const admin = require("firebase-admin");
const db = admin.firestore();

// Create test user
await db.collection("users").doc("test-user-id").set({
  ton_wallet_address: "UQCWcm7v_aEFlLEUmAYuryEfpepeRcFVSVIimfmCYmxbE7gI",
  balance: 10.0,
  locked_balance: 0,
});

// Create app config
await db.collection("app_config").doc("fees").set({
  deposit_fee: 0.1,
  withdrawal_fee: 0.1,
  min_deposit_amount: 0.1,
  min_withdrawal_amount: 0.1,
  max_withdrawal_amount: 100,
});
```

### 2. Test Valid Withdrawal

```javascript
withdrawFunds({ amount: 1.5 }, { auth: { uid: "test-user-id" } });
```

### 3. Test Error Cases

```javascript
// Test insufficient balance
withdrawFunds({ amount: 20 }, { auth: { uid: "test-user-id" } });

// Test invalid amount
withdrawFunds({ amount: -1 }, { auth: { uid: "test-user-id" } });

// Test unauthenticated
withdrawFunds({ amount: 1.5 }, {});
```

## Environment Variables for Local Development

The functions use Firebase Functions config in production, but for local development, you can use environment variables in `functions/.env.local`:

```env
# Required for withdrawFunds function
TON_RPC_URL_MAINNET=https://misty-crimson-friday.ton-mainnet.quiknode.pro/bd21cc29f2080adf31da2fe2af76f1f6df3d7b5c
TON_MARKETPLACE_WALLET=UQCWcm7v_aEFlLEUmAYuryEfpepeRcFVSVIimfmCYmxbE7gI
TON_MARKETPLACE_WALLET_MNEMONIC="spice myth collect display ship legend seminar injury setup voice faith steel erase captain supply cave range author neither decrease hire update mechanic state"
FIREBASE_PROJECT_ID=your-project-id
NODE_ENV=development
```

### Loading Environment Variables in Emulator

Create a modified config.ts for local development that reads from process.env when in emulator mode:

```typescript
// Add to functions/src/config.ts for local development
export function getConfigLocal(): AppConfig {
  if (
    process.env.NODE_ENV === "development" &&
    process.env.FUNCTIONS_EMULATOR
  ) {
    return {
      app: {
        environment: process.env.NODE_ENV || "development",
        project_id: process.env.FIREBASE_PROJECT_ID || "",
      },
      ton: {
        rpc_url_mainnet: process.env.TON_RPC_URL_MAINNET,
        marketplace_wallet: process.env.TON_MARKETPLACE_WALLET || "",
        marketplace_wallet_mnemonic:
          process.env.TON_MARKETPLACE_WALLET_MNEMONIC || "",
        network: "mainnet",
      },
    };
  }
  return getConfig(); // Fall back to Firebase config
}
```

## Common Issues & Solutions

### 1. "Function not found" Error

- Ensure functions are built: `npm run build`
- Check `functions/lib/index.js` exists
- Verify function is exported in `src/index.ts`

### 2. Authentication Errors

- Use Auth emulator: http://localhost:9099
- Create test users via Emulator UI
- Pass correct auth context in function calls

### 3. Firestore Connection Issues

- Ensure Firestore emulator is running
- Check firebase.json emulator configuration
- Verify admin SDK initialization

### 4. TON Network Errors

- Check TON_RPC_URL environment variable
- Verify wallet mnemonic is correct
- Test with testnet first if needed

## Production Debugging

### View Production Logs

```bash
firebase functions:log --only withdrawFunds
```

### Deploy with Debug Logs

```bash
firebase deploy --only functions
```

## Useful Commands

```bash
# Build and watch for changes
npm run build:watch

# Start only functions emulator
firebase emulators:start --only functions

# Start functions shell
npm run shell

# View function logs
firebase functions:log

# Deploy functions
npm run deploy
```

export function extractOrderIdFromUrl(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  const urlParams = new URLSearchParams(window.location.search);

  const startParam = urlParams.get('tgWebAppStartParam');
  if (startParam?.startsWith('order_')) {
    return startParam.replace('order_', '');
  }
  return null;
}

export function extractOrderIdFromTelegram(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  const telegramWebApp = window.Telegram?.WebApp;

  if (telegramWebApp) {
    let startParam: string | null | undefined =
      telegramWebApp.initDataUnsafe?.start_param;

    if (!startParam) {
      const urlParams = new URLSearchParams(window.location.search);
      startParam = urlParams.get('tgWebAppStartParam');
    }

    if (startParam?.startsWith('order_')) {
      return startParam.replace('order_', '');
    }
  }

  return null;
}

export function handleOrderDeepLink(): string | null {
  // Try to extract order ID from Telegram first, then from URL
  const orderIdFromTelegram = extractOrderIdFromTelegram();
  const orderIdFromUrl = extractOrderIdFromUrl();

  const orderId = orderIdFromTelegram || orderIdFromUrl;

  if (orderId) {
    console.log('Order deep link detected:', orderId);
    // Navigate to the order page
    if (typeof window !== 'undefined') {
      window.location.href = `/orders/${orderId}`;
    }
  }

  return orderId;
}

export function generateOrderShareLink(orderId: string): string {
  const botName = process.env.NEXT_PUBLIC_TELEGRAM_BOT_NAME;

  if (!botName) {
    throw new Error('Bot name must be configured in environment variables');
  }

  return `https://t.me/${botName}?startapp=order_${orderId}`;
}

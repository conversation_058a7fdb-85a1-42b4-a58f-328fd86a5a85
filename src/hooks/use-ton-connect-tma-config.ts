'use client';

import { useTonConnectUI } from '@tonconnect/ui-react';
import { useEffect } from 'react';

export function useTonConnectTMAConfig() {
  const [tonConnectUI] = useTonConnectUI();

  useEffect(() => {
    if (!tonConnectUI) return;

    const botUsername = process.env.NEXT_PUBLIC_TELEGRAM_BOT_NAME;

    tonConnectUI.uiOptions = {
      actionsConfiguration: {
        twaReturnUrl: `https://t.me/${botUsername}`,
        returnStrategy: 'back',
        skipRedirectToWallet: 'ios',
      },
    };
  }, [tonConnectUI]);
}

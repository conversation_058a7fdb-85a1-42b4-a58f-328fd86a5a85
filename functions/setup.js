#!/usr/bin/env node

const fs = require("fs");
const path = require("path");

console.log("🔧 Firebase Functions Setup");

function setupEnvironment(environment = "production") {
  console.log(`\n📋 Setting up ${environment} environment...`);

  const sourceFile =
    environment === "development" ? ".env.development" : ".env.production";
  const targetFile = ".env";

  const sourcePath = path.join(__dirname, sourceFile);
  const targetPath = path.join(__dirname, targetFile);

  if (!fs.existsSync(sourcePath)) {
    console.error(`❌ ${sourceFile} file not found. Please create one.`);
    process.exit(1);
  }

  try {
    // Copy the environment file to .env
    fs.copyFileSync(sourcePath, targetPath);
    console.log(`✅ Environment: ${sourceFile} → ${targetFile}`);

    // Read and validate the environment variables
    const envContent = fs.readFileSync(targetPath, "utf8");
    const envVars = {};

    envContent.split("\n").forEach((line) => {
      line = line.trim();
      if (line && !line.startsWith("#")) {
        const [key, ...valueParts] = line.split("=");
        if (key && valueParts.length > 0) {
          envVars[key.trim()] = valueParts.join("=").trim();
        }
      }
    });

    // Validate required environment variables
    const requiredVars = [
      "TELEGRAM_BOT_TOKEN",
      "TELEGRAM_LOCAL_BOT_TOKEN",
      "TELEGRAM_API_ID",
      "TELEGRAM_API_HASH",
      "TON_MARKETPLACE_WALLET",
      "TON_MARKETPLACE_WALLET_MNEMONIC",
    ];

    let hasErrors = false;
    requiredVars.forEach((varName) => {
      if (!envVars[varName]) {
        console.error(`❌ Required environment variable ${varName} is missing`);
        hasErrors = true;
      }
    });

    if (hasErrors) {
      console.error(
        "\n❌ Please fix the missing environment variables and try again."
      );
      process.exit(1);
    }

    console.log(
      `✅ Validation: ${
        Object.keys(envVars).length
      } environment variables found`
    );
    console.log(`✅ Setup complete for ${environment}!`);

    console.log("\n🚀 Ready to deploy:");
    console.log("   firebase deploy --only functions");
  } catch (error) {
    console.error(`❌ Setup failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  const environment = process.argv[2] || "production";

  if (environment !== "development" && environment !== "production") {
    console.error("❌ Invalid environment. Use 'development' or 'production'");
    process.exit(1);
  }

  setupEnvironment(environment);
}

module.exports = { setupEnvironment };

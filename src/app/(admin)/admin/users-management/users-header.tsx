import { RefreshCw, Users } from 'lucide-react';

import { Button } from '@/components/ui/button';

interface UsersHeaderProps {
  onRefresh: () => void;
  totalUsers: number;
}

export function UsersHeader({ onRefresh, totalUsers }: UsersHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <Users className="w-6 h-6 text-[#6ab2f2]" />
        <div>
          <h2 className="text-xl font-semibold text-white">Users Management</h2>
          <p className="text-sm text-[#708499]">
            Total users: {totalUsers.toLocaleString()}
          </p>
        </div>
      </div>

      <Button onClick={onRefresh} variant="outline" size="sm" className="gap-2">
        <RefreshCw className="w-4 h-4" />
        Refresh
      </Button>
    </div>
  );
}

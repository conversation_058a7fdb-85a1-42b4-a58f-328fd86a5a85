'use client';

import { Calendar, TrendingUp, User } from 'lucide-react';
import { useEffect, useState } from 'react';

import {
  getResellHistoryByOrderId,
  type ResellTxHistoryWithUsers,
} from '@/api/resell-tx-history-api';
import { TonPriceDisplay } from '@/components/shared/ton-price-display';
import { TonLogo } from '@/components/TonLogo';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { OrderEntity } from '@/core.constants';
import { useRootContext } from '@/root-context';

interface ResellTxHistoryProps {
  order: OrderEntity;
  onClose: () => void;
}

interface ResellTxHistoryItemProps {
  transaction: ResellTxHistoryWithUsers;
  isOriginalSeller: boolean;
  order: OrderEntity;
}

function ResellTxHistoryItem({
  transaction,
  isOriginalSeller,
  order,
}: ResellTxHistoryItemProps) {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const calculateSellerEarnings = () => {
    if (!isOriginalSeller || !order.fees?.resell_purchase_fee_for_seller) {
      return 0;
    }
    const executionPrice = parseFloat(transaction.execution_price);
    const feeBPS = order.fees.resell_purchase_fee_for_seller;
    return (executionPrice * feeBPS) / 10000; // Convert BPS to decimal
  };

  const sellerEarnings = calculateSellerEarnings();

  return (
    <Card className="bg-[#2a3441] border-[#3a4a5c]/30">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-[#6ab2f2]" />
            <span className="text-sm font-medium text-[#f5f5f5]">
              Resell Transaction
            </span>
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3 text-[#708499]" />
            <span className="text-xs text-[#708499]">
              {formatDate(transaction.executed_at)}
            </span>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-[#708499]">Execution Price</span>
            <div className="flex items-center gap-1">
              <TonLogo className="w-4 h-4" />
              <TonPriceDisplay
                amount={parseFloat(transaction.execution_price)}
                className="text-[#f5f5f5] font-medium"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="flex items-center gap-1 mb-1">
                <User className="h-3 w-3 text-[#708499]" />
                <span className="text-xs text-[#708499]">Reseller</span>
              </div>
              <div className="text-sm text-[#f5f5f5]">
                {transaction.reseller?.displayName ??
                  transaction.reseller?.email ??
                  `User ${transaction.reseller_id.slice(0, 8)}...`}
              </div>
              {transaction.reseller?.telegram_handle && (
                <div className="text-xs text-[#708499]">
                  @{transaction.reseller.telegram_handle}
                </div>
              )}
            </div>

            <div>
              <div className="flex items-center gap-1 mb-1">
                <User className="h-3 w-3 text-[#708499]" />
                <span className="text-xs text-[#708499]">Buyer</span>
              </div>
              <div className="text-sm text-[#f5f5f5]">
                {transaction.buyer?.displayName ??
                  transaction.buyer?.email ??
                  `User ${transaction.buyer_id.slice(0, 8)}...`}
              </div>
              {transaction.buyer?.telegram_handle && (
                <div className="text-xs text-[#708499]">
                  @{transaction.buyer.telegram_handle}
                </div>
              )}
            </div>
          </div>

          {isOriginalSeller && sellerEarnings > 0 && (
            <div className="pt-2 border-t border-[#3a4a5c]/30">
              <div className="flex items-center justify-between">
                <span className="text-sm text-[#708499]">Your Earnings</span>
                <div className="flex items-center gap-1">
                  <TonLogo className="w-4 h-4" />
                  <TonPriceDisplay
                    amount={sellerEarnings}
                    className="text-green-400 font-medium"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function ResellTxHistory({ order, onClose }: ResellTxHistoryProps) {
  const { currentUser } = useRootContext();
  const [transactions, setTransactions] = useState<ResellTxHistoryWithUsers[]>(
    [],
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isOriginalSeller = currentUser?.id === order.sellerId;

  useEffect(() => {
    const fetchResellHistory = async () => {
      if (!order.id) return;

      try {
        setLoading(true);
        setError(null);
        const response = await getResellHistoryByOrderId(order.id);

        if (response.success) {
          setTransactions(response.transactions);
        } else {
          setError(response.message ?? 'Failed to fetch resell history');
        }
      } catch (err) {
        console.error('Error fetching resell history:', err);
        setError('Failed to fetch resell history');
      } finally {
        setLoading(false);
      }
    };

    fetchResellHistory();
  }, [order.id]);

  if (loading) {
    return (
      <Card className="bg-[#2a3441] border-[#3a4a5c]/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#f5f5f5]">
            <TrendingUp className="h-5 w-5" />
            Resell History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <div className="animate-spin h-6 w-6 border-2 border-[#6ab2f2] border-t-transparent rounded-full mx-auto mb-2"></div>
            <p className="text-[#708499]">Loading resell history...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="bg-[#2a3441] border-[#3a4a5c]/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#f5f5f5]">
            <TrendingUp className="h-5 w-5" />
            Resell History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button
            onClick={onClose}
            variant="outline"
            className="w-full mt-4 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#3a4a5c]/20"
          >
            Close
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (transactions.length === 0) {
    return (
      <Card className="bg-[#2a3441] border-[#3a4a5c]/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-[#f5f5f5]">
            <TrendingUp className="h-5 w-5" />
            Resell History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">
            <TrendingUp className="h-8 w-8 text-[#708499] mx-auto mb-2 opacity-50" />
            <p className="text-[#708499]">No resell transactions found</p>
          </div>
          <Button
            onClick={onClose}
            variant="outline"
            className="w-full mt-4 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#3a4a5c]/20"
          >
            Close
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-[#2a3441] border-[#3a4a5c]/30">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-[#f5f5f5]">
          <TrendingUp className="h-5 w-5" />
          Resell History ({transactions.length})
        </CardTitle>
        {isOriginalSeller && (
          <p className="text-sm text-[#708499]">
            Showing your earnings from each resell transaction
          </p>
        )}
      </CardHeader>
      <CardContent className="space-y-3">
        {transactions.map((transaction) => (
          <ResellTxHistoryItem
            key={transaction.id}
            transaction={transaction}
            isOriginalSeller={isOriginalSeller}
            order={order}
          />
        ))}
        <Button
          onClick={onClose}
          variant="outline"
          className="w-full mt-4 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#3a4a5c]/20"
        >
          Close
        </Button>
      </CardContent>
    </Card>
  );
}

import { Request, Response, NextFunction } from "express";
import { log } from "../utils/logger";

export interface AuthenticatedRequest extends Request {
  isAuthenticated: boolean;
}

export function authenticateHealthCheck(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  const authHeader = req.headers.authorization;
  const expectedToken = process.env.HEALTH_CHECK_BEARER_TOKEN;

  // Check if Bearer token is configured
  if (!expectedToken) {
    log.error("Health check authentication token not configured", undefined, {
      operation: "health_check_auth",
      error: "missing_token_config",
    });
    res.status(500).json({
      error: "Internal Server Error",
      message: "Authentication not properly configured",
      timestamp: new Date().toISOString(),
    });
    return;
  }

  // Check if Authorization header is present
  if (!authHeader) {
    log.warn("Health check request missing Authorization header", {
      operation: "health_check_auth",
      ip: req.ip,
      userAgent: req.get("User-Agent"),
    });
    res.status(401).json({
      error: "Unauthorized",
      message: "Authorization header required",
      timestamp: new Date().toISOString(),
    });
    return;
  }

  // Check if Authorization header follows Bearer token format
  const tokenMatch = authHeader.match(/^Bearer\s+(.+)$/);
  if (!tokenMatch) {
    log.warn("Health check request with invalid Authorization header format", {
      operation: "health_check_auth",
      ip: req.ip,
      userAgent: req.get("User-Agent"),
      authHeaderFormat: authHeader.substring(0, 20) + "...",
    });
    res.status(401).json({
      error: "Unauthorized",
      message: "Invalid Authorization header format. Expected: Bearer <token>",
      timestamp: new Date().toISOString(),
    });
    return;
  }

  const providedToken = tokenMatch[1];

  // Validate token exists
  if (!providedToken) {
    log.warn("Health check request with empty Bearer token", {
      operation: "health_check_auth",
      ip: req.ip,
      userAgent: req.get("User-Agent"),
    });
    res.status(401).json({
      error: "Unauthorized",
      message: "Bearer token cannot be empty",
      timestamp: new Date().toISOString(),
    });
    return;
  }

  // Constant-time comparison to prevent timing attacks
  if (!constantTimeCompare(providedToken, expectedToken)) {
    log.warn("Health check request with invalid Bearer token", {
      operation: "health_check_auth",
      ip: req.ip,
      userAgent: req.get("User-Agent"),
      tokenLength: providedToken.length,
    });
    res.status(403).json({
      error: "Forbidden",
      message: "Invalid authentication token",
      timestamp: new Date().toISOString(),
    });
    return;
  }

  // Authentication successful
  log.info("Health check request authenticated successfully", {
    operation: "health_check_auth",
    ip: req.ip,
    userAgent: req.get("User-Agent"),
  });

  (req as AuthenticatedRequest).isAuthenticated = true;
  next();
}

function constantTimeCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }

  return result === 0;
}

export function addSecurityHeaders(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  res.setHeader("X-Frame-Options", "DENY");

  res.setHeader("X-Content-Type-Options", "nosniff");

  res.setHeader("X-XSS-Protection", "1; mode=block");

  if (req.secure || req.get("X-Forwarded-Proto") === "https") {
    res.setHeader(
      "Strict-Transport-Security",
      "max-age=31536000; includeSubDomains"
    );
  }

  next();
}

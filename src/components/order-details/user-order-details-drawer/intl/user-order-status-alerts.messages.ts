import { defineMessages } from 'react-intl';

export const userOrderStatusAlertsMessages = defineMessages({
  freezePeriodActive: {
    id: 'userOrderStatusAlerts.freezePeriodActive',
    defaultMessage: 'Freeze Period Active',
  },
  freezePeriodDescription: {
    id: 'userOrderStatusAlerts.freezePeriodDescription',
    defaultMessage:
      'Collection items cannot be transferred yet. Wait for the freeze period to end.',
  },
  waitingForTransfer: {
    id: 'userOrderStatusAlerts.waitingForTransfer',
    defaultMessage: 'Waiting for Transfer',
  },
  waitingForTransferDescription: {
    id: 'userOrderStatusAlerts.waitingForTransferDescription',
    defaultMessage: 'Wait until the collection item becomes transferable.',
  },
  readyToSend: {
    id: 'userOrderStatusAlerts.readyToSend',
    defaultMessage: 'Ready to Send',
  },
  readyToSendDescription: {
    id: 'userOrderStatusAlerts.readyToSendDescription',
    defaultMessage: 'You can now send the gift to the relayer.',
  },
  giftReady: {
    id: 'userOrderStatusAlerts.giftReady',
    defaultMessage: 'Gift Ready!',
  },
  giftReadyDescription: {
    id: 'userOrderStatusAlerts.giftReadyDescription',
    defaultMessage:
      'Your gift has been sent to the relayer. Please visit the bot to claim your gift.',
  },
  openBotToClaim: {
    id: 'userOrderStatusAlerts.openBotToClaim',
    defaultMessage: 'Open Bot to Claim',
  },
  giftRefundAvailable: {
    id: 'userOrderStatusAlerts.giftRefundAvailable',
    defaultMessage: 'Gift Refund Available',
  },
  giftRefundDescription: {
    id: 'userOrderStatusAlerts.giftRefundDescription',
    defaultMessage: 'Go to the relayer to refund your gift.',
  },
  openBotForRefund: {
    id: 'userOrderStatusAlerts.openBotForRefund',
    defaultMessage: 'Open Bot for Refund',
  },
});

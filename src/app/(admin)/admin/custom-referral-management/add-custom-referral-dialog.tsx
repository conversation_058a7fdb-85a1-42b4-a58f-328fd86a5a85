import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { AlertTriangle } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { getUserById, updateUser } from '@/api/user-api';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import type { UserEntity } from '@/core.constants';
import { useToast } from '@/hooks/use-toast';

import {
  type CustomReferralFormData,
  customReferralSchema,
} from './custom-referral-types';
import { getCustomReferralWarnings } from './custom-referral-utils';
import { TelegramUserSearch } from './telegram-user-search';

interface AddCustomReferralDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  maxPurchaseFee: number;
  maxResellPurchaseFee: number;
}

export const AddCustomReferralDialog = ({
  isOpen,
  onClose,
  onSuccess,
  maxPurchaseFee,
  maxResellPurchaseFee,
}: AddCustomReferralDialogProps) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [telegramHandleSearch, setTelegramHandleSearch] = useState('');

  const form = useForm<CustomReferralFormData>({
    resolver: zodResolver(customReferralSchema),
    defaultValues: {
      userId: '',
      referralFee: 0,
    },
  });

  const referralFee = form.watch('referralFee');
  const warnings = getCustomReferralWarnings(
    referralFee,
    maxPurchaseFee,
    maxResellPurchaseFee,
  );

  const handleUserSelect = (user: UserEntity) => {
    form.setValue('userId', user.id);
    setTelegramHandleSearch(user.telegram_handle || '');
  };

  const resetForm = () => {
    setTelegramHandleSearch('');
    form.reset();
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const onSubmit = async (data: CustomReferralFormData) => {
    if (data.referralFee > maxPurchaseFee) {
      toast({
        title: 'Error',
        description: `Referral fee cannot be more than purchase fee (${maxPurchaseFee} BPS)`,
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoading(true);

      const user = await getUserById(data.userId);
      if (!user) {
        toast({
          title: 'Error',
          description: 'User not found',
          variant: 'destructive',
        });
        return;
      }

      await updateUser(data.userId, {
        referral_fee: data.referralFee,
      });

      toast({
        title: 'Success',
        description: 'Custom referral fee set successfully',
      });

      resetForm();
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error setting custom referral fee:', error);
      toast({
        title: 'Error',
        description: 'Failed to set custom referral fee',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Set Custom Referral Fee</DialogTitle>
          <DialogDescription>
            Set a custom referral fee for a specific user. The fee cannot exceed
            the purchase fee ({maxPurchaseFee} BPS).
          </DialogDescription>
        </DialogHeader>

        {warnings.length > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Configuration Warnings</AlertTitle>
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1">
                {warnings.map((warning) => (
                  <li key={warning} className="text-sm">
                    {warning}
                  </li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <TelegramUserSearch
              value={telegramHandleSearch}
              onUserSelect={handleUserSelect}
              onSearchChange={setTelegramHandleSearch}
            />

            <FormField
              control={form.control}
              name="userId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>User ID</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter user ID" {...field} />
                  </FormControl>
                  <FormDescription>
                    The Firebase user ID of the user
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="referralFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Referral Fee (BPS)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="200"
                      {...field}
                      onChange={(e) =>
                        field.onChange(Number(e.target.value) || 0)
                      }
                    />
                  </FormControl>
                  <FormDescription>
                    Custom referral fee in basis points (100 BPS = 1%)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Setting...' : 'Set Custom Fee'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

import { Caption } from '@telegram-apps/telegram-ui';
import { useIntl } from 'react-intl';

import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';

import { depositDrawerActionsMessages } from './intl/deposit-drawer-actions.messages';

interface DepositDrawerActionsProps {
  onDeposit: () => void;
  onCancel: () => void;
  isValidAmount: boolean;
  loading: boolean;
  isWalletConnected: boolean;
  depositAmount: string;
  totalAmount?: number;
}

export function DepositDrawerActions({
  onDeposit,
  onCancel,
  isValidAmount,
  loading,
  isWalletConnected,
  depositAmount,
  totalAmount,
}: DepositDrawerActionsProps) {
  const { formatMessage: t } = useIntl();
  const isDepositDisabled = !isValidAmount || loading || !isWalletConnected;

  return (
    <div className="space-y-3 pt-4">
      <Button
        onClick={onDeposit}
        disabled={isDepositDisabled}
        className="w-full h-12 bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white border-0 rounded-2xl"
      >
        {loading ? (
          t(depositDrawerActionsMessages.processing)
        ) : (
          <>
            {t(depositDrawerActionsMessages.deposit)}{' '}
            {depositAmount && isValidAmount && totalAmount ? (
              <>
                &#40;
                {totalAmount.toFixed(1)} <TonLogo className="-m-2" size={24} />
                <span className="-ml-1 translate-x-[1px]">&#41;</span>
              </>
            ) : (
              ''
            )}
          </>
        )}
      </Button>

      <Button
        variant="outline"
        onClick={onCancel}
        className="w-full h-12 bg-transparent border-[#3a4a5c]/50 text-[#708499] hover:bg-[#232e3c]/50 hover:text-[#f5f5f5] rounded-2xl"
        disabled={loading}
      >
        {t(depositDrawerActionsMessages.cancel)}
      </Button>

      {!isWalletConnected && (
        <div className="text-center mt-4">
          <Caption level="2" weight="3" className="text-[#ec3942]">
            {t(depositDrawerActionsMessages.pleaseConnectWallet)}
          </Caption>
        </div>
      )}
    </div>
  );
}

import { Caption } from '@telegram-apps/telegram-ui';

import { CollectionName } from '@/components/shared/collection-name';
import type { CollectionEntity, OrderEntity } from '@/core.constants';
import { getOrderDisplayNumber } from '@/services/order-service';

interface UserOrderCardInfoProps {
  order: OrderEntity;
  collection: CollectionEntity | null;
}

export function UserOrderCardInfo({
  order,
  collection,
}: UserOrderCardInfoProps) {
  return (
    <div className="flex items-center justify-between mb-1">
      <Caption level="1" weight="1" className="truncate">
        <CollectionName collection={collection} />
      </Caption>
      <Caption level="2" weight="3" className="w-fit text-[#78797e]">
        {getOrderDisplayNumber(order)}
      </Caption>
    </div>
  );
}

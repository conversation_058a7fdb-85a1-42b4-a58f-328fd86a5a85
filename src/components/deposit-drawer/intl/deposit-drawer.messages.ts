import { defineMessages } from 'react-intl';

export const depositDrawerMessages = defineMessages({
  depositFunds: {
    id: 'depositDrawer.depositFunds',
    defaultMessage: 'Deposit Funds',
  },
  addTonToBalance: {
    id: 'depositDrawer.addTonToBalance',
    defaultMessage: 'Add TON to your marketplace balance',
  },
  loadingConfiguration: {
    id: 'depositDrawer.loadingConfiguration',
    defaultMessage: 'Loading configuration...',
  },
  depositInformation: {
    id: 'depositDrawer.depositInformation',
    defaultMessage: 'Deposit Information',
  },
  minimumDeposit: {
    id: 'depositDrawer.minimumDeposit',
    defaultMessage: 'Minimum deposit:',
  },
  depositFee: {
    id: 'depositDrawer.depositFee',
    defaultMessage: 'Deposit fee:',
  },
  depositProcessing: {
    id: 'depositDrawer.depositProcessing',
    defaultMessage: 'Deposit Processing',
  },
  youWillReceiveFundsWithin: {
    id: 'depositDrawer.youWillReceiveFundsWithin',
    defaultMessage: 'You will receive your funds within',
  },
});

import type { Control, FieldPath } from 'react-hook-form';

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { NumberInput } from '@/components/ui/number-input';

import type { FeeFieldConfig, FeesFormData } from './fees-management-types';
import { formatBpsToPercent, formatTonValue } from './fees-management-utils';

interface FeesFormFieldProps {
  control: Control<FeesFormData>;
  config: FeeFieldConfig;
}

export const FeesFormField = ({ control, config }: FeesFormFieldProps) => {
  const { name, label, description, type } = config;

  return (
    <FormField
      control={control}
      name={name as FieldPath<FeesFormData>}
      render={({ field }) => {
        console.log('field.value - field.name: ', field.name, field.value);

        return (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormControl>
              <NumberInput
                className="border-2 border-gray-300 focus:border-blue-500"
                value={field.value}
                onValueChange={(value) => {
                  if (!value) {
                    field.onChange('');
                    return;
                  }

                  field.onChange(value);
                }}
                decimalScale={type === 'ton' ? 9 : type === 'days' ? 0 : 2}
                fixedDecimalScale={false}
                min={0}
                stepper={type === 'ton' ? 0.1 : type === 'days' ? 1 : 0.01}
                thousandSeparator=","
              />
            </FormControl>
            <FormDescription>
              {description}. Current:{' '}
              {type === 'ton'
                ? `${formatTonValue(field.value)} TON`
                : type === 'days'
                  ? `${field.value} days`
                  : `${formatBpsToPercent(field.value)}%`}
            </FormDescription>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
};

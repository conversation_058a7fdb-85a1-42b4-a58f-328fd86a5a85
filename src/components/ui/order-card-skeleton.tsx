'use client';

import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface OrderCardSkeletonProps {
  className?: string;
}

export function OrderCardSkeleton({ className }: OrderCardSkeletonProps) {
  return (
    <Card
      className={cn('bg-[#232e3c] border-[#3a4a5c] animate-pulse', className)}
    >
      <CardContent className="p-2 flex flex-col h-full">
        <div className="aspect-square relative rounded-lg overflow-hidden bg-[#17212b] mb-2">
          <div className="w-full h-full bg-[#3a4a5c] animate-pulse" />

          <div className="absolute top-2 left-2">
            <div className="w-16 h-5 bg-[#3a4a5c] rounded animate-pulse" />
          </div>
        </div>

        <div className="flex-1 space-y-2">
          <div className="h-3 bg-[#3a4a5c] rounded animate-pulse w-1/2" />

          <div className="h-5 bg-[#3a4a5c] rounded animate-pulse w-2/3" />
        </div>

        <div className="mt-2">
          <div className="w-full h-8 bg-[#3a4a5c] rounded-full animate-pulse" />
        </div>
      </CardContent>
    </Card>
  );
}

interface OrderCardSkeletonGridProps {
  count?: number;
  gridCols?: string;
  className?: string;
}

export function OrderCardSkeletonGrid({
  count = 8,
  gridCols = 'grid-cols-2 md:grid-cols-4',
  className,
}: OrderCardSkeletonGridProps) {
  return (
    <div className={cn(`grid ${gridCols} gap-2`, className)}>
      {Array.from({ length: count }).map((_, index) => (
        <OrderCardSkeleton key={index} />
      ))}
    </div>
  );
}

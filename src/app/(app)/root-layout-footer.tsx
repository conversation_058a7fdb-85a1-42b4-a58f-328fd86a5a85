'use client';

import { ShoppingCart, Store, User } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useIntl } from 'react-intl';

import { AppRoutes } from '@/core.constants';
import { cn } from '@/lib/utils';

import { rootLayoutFooterMessages } from './intl/root-layout-footer.messages';

export default function RootLayoutFooter() {
  const { formatMessage: t } = useIntl();
  const pathname = usePathname();

  const allNavItems = [
    {
      icon: Store,
      label: t(rootLayoutFooterMessages.marketplace),
      route: AppRoutes.MARKETPLACE,
      active: pathname === AppRoutes.MARKETPLACE,
    },
    {
      icon: ShoppingCart,
      label: t(rootLayoutFooterMessages.myOrders),
      route: AppRoutes.ORDERS,
      active: pathname === AppRoutes.ORDERS,
    },
    {
      icon: User,
      label: t(rootLayoutFooterMessages.myProfile),
      route: AppRoutes.PROFILE,
      active: pathname === AppRoutes.PROFILE,
    },
  ];

  const navItems = allNavItems;

  return (
    <footer className="fixed bottom-0 left-0 right-0 bg-[#17212b] text-[#f5f5f5] border-t border-[#3a4a5c] z-50">
      <div className={cn('flex items-center justify-between h-full')}>
        {navItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <Link
              key={item.route}
              href={item.route}
              className={`flex flex-col flex-1/3 items-center gap-1 p-3 pb-6 h-auto transition-colors ${
                item.active
                  ? 'text-[#6ab2f2]'
                  : 'text-[#708499] hover:text-[#f5f5f5]'
              }`}
            >
              <IconComponent className="w-5 h-5" />
              <span className="text-xs font-medium leading-tight">
                {item.label}
              </span>
            </Link>
          );
        })}
      </div>
    </footer>
  );
}

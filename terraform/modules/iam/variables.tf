variable "project_id" {
  description = "GCP project ID"
  type        = string
}

variable "terraform_roles" {
  description = "IAM roles for Terraform service account"
  type        = set(string)
  default = [
    "roles/run.admin",
    "roles/iam.serviceAccountAdmin",
    "roles/iam.serviceAccountUser",
    "roles/artifactregistry.admin",
    "roles/serviceusage.serviceUsageAdmin",
    "roles/resourcemanager.projectIamAdmin",
    "roles/cloudbuild.builds.builder",
    "roles/redis.admin",
    "roles/compute.networkViewer"
  ]
}

variable "cloud_run_service_roles" {
  description = "IAM roles for Cloud Run service account"
  type        = set(string)
  default = [
    "roles/logging.logWriter",
    "roles/monitoring.metricWriter",
    "roles/cloudtrace.agent",
    "roles/redis.editor"
  ]
}

variable "github_actions_roles" {
  description = "IAM roles for GitHub Actions service account"
  type        = set(string)
  default = [
    "roles/run.developer",
    "roles/artifactregistry.writer",
    "roles/iam.serviceAccountUser",
    "roles/serviceusage.serviceUsageConsumer"
  ]
}

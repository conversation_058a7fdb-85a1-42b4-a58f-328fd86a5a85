'use client';

import {
  bindThemeParamsCssVars,
  mountMiniAppSync,
} from '@telegram-apps/sdk-react';
import { useEffect } from 'react';

export function useTelegramTheme() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const timeoutId = setTimeout(() => {
      try {
        if (mountMiniAppSync.isAvailable()) {
          mountMiniAppSync();
          bindThemeParamsCssVars();
        }
      } catch (error) {
        console.warn('Failed to bind Telegram theme variables:', error);
      }
    }, 100);

    return () => clearTimeout(timeoutId);
  }, []);
}

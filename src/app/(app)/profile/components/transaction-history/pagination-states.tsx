import { Loader2 } from 'lucide-react';
import { useIntl } from 'react-intl';

import { paginationStatesMessages } from './intl/pagination-states.messages';

interface LoadingMoreStateProps {
  isVisible: boolean;
}

export function LoadingMoreState({ isVisible }: LoadingMoreStateProps) {
  const { formatMessage: t } = useIntl();

  if (!isVisible) return null;

  return (
    <div className="flex flex-col items-center justify-center py-6 space-y-2">
      <Loader2 className="w-6 h-6 animate-spin text-[#708499]" />
      <p className="text-[#708499] text-sm">
        {t(paginationStatesMessages.loadingMoreTransactions)}
      </p>
    </div>
  );
}

interface EndStateProps {
  isVisible: boolean;
}

export function EndState({ isVisible }: EndStateProps) {
  const { formatMessage: t } = useIntl();

  if (!isVisible) return null;

  return (
    <div className="text-center py-4">
      <p className="text-[#708499] text-sm">
        {t(paginationStatesMessages.reachedEndOfHistory)}
      </p>
    </div>
  );
}

'use client';

import { useEffect, useRef } from 'react';

interface UseVisualViewportOptions {
  enabled?: boolean;
  offset?: number;
}

export function useVisualViewport(options: UseVisualViewportOptions = {}) {
  const { enabled = true, offset = 0 } = options;
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!enabled || typeof window === 'undefined' || !window.visualViewport) {
      return;
    }

    function onVisualViewportChange() {
      if (!elementRef.current) return;

      const visualViewportHeight = window.visualViewport!.height;
      const diffFromInitial = window.innerHeight - visualViewportHeight;

      elementRef.current.style.height = `${Math.min(visualViewportHeight - offset, window.innerHeight * 0.9)}px`;

      elementRef.current.style.bottom = `${Math.max(diffFromInitial, 0)}px`;
    }

    if (elementRef.current) {
      elementRef.current.style.height = `${Math.min(window.innerHeight - offset, window.innerHeight * 0.9)}px`;
    }

    window.visualViewport.addEventListener('resize', onVisualViewportChange);

    return () => {
      window.visualViewport?.removeEventListener(
        'resize',
        onVisualViewportChange,
      );
    };
  }, [enabled, offset]);

  return elementRef;
}

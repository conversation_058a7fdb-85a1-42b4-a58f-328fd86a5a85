import { globalCache } from '@/utils/cache-utils';

const CACHE_CONFIG = { duration: 30 * 60 * 1000 }; // 30 minutes

export const getCachedTgsData = (tgsUrl: string): object | null => {
  return globalCache.get<object>(`tgs:${tgsUrl}`);
};

export const setCachedTgsData = (tgsUrl: string, lottieJson: object): void => {
  globalCache.set(`tgs:${tgsUrl}`, lottie<PERSON><PERSON>, CACHE_CONFIG);
};

export const clearTgsCache = (): void => {
  globalCache.invalidatePattern('tgs:');
};

export const getTgsCacheStats = () => {
  return {
    message: 'TGS cache now uses global cache manager',
    cacheDurationMinutes: CACHE_CONFIG.duration / (60 * 1000),
  };
};

export const cleanupExpiredTgsCache = (): number => {
  globalCache.clearExpired();
  return 0; // Global cache handles cleanup internally
};

if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as typeof window & { tgsCache: unknown }).tgsCache = {
    getStats: getTgsCacheStats,
    clear: clearTgsCache,
    cleanup: cleanupExpiredTgsCache,
  };
}

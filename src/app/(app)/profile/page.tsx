'use client';

import { useState } from 'react';
import { useIntl } from 'react-intl';

import { LanguageSelector } from '@/components/ui/language-selector';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import { ProfileReferralSection } from './components/profile-referral-section';
import { ProfileUserInfo } from './components/profile-user-info';
import { SocialLinks } from './components/social-links';
import { TransactionHistoryTable } from './components/transaction-history-table';
import { profileMessages } from './profile.messages';

export default function Profile() {
  const { formatMessage: t } = useIntl();
  const [activeTab, setActiveTab] = useState('main');

  return (
    <div className="max-w-4xl mx-auto space-y-4">
      <div className="flex justify-end">
        <LanguageSelector />
      </div>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="main">{t(profileMessages.main)}</TabsTrigger>
          <TabsTrigger value="transactions">
            {t(profileMessages.myTransactions)}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="main" className="space-y-2">
          <ProfileUserInfo />
          <ProfileReferralSection />
          <SocialLinks />
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <TransactionHistoryTable />
        </TabsContent>
      </Tabs>
    </div>
  );
}

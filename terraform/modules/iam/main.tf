# Service accounts
resource "google_service_account" "terraform_sa" {
  account_id   = "terraform-sa"
  display_name = "Terraform Service Account"
  description  = "Service account for Terraform to manage all resources"
  project      = var.project_id
}

resource "google_service_account" "cloud_run_service_sa" {
  account_id   = "cloud-run-service-sa"
  display_name = "Cloud Run Service Account"
  description  = "Service account for the Cloud Run service runtime"
  project      = var.project_id
}

resource "google_service_account" "github_actions_sa" {
  account_id   = "github-actions-sa"
  display_name = "GitHub Actions Deployment SA"
  description  = "Service account for GitHub Actions to deploy to Cloud Run"
  project      = var.project_id
}

# IAM roles for service accounts
resource "google_project_iam_member" "terraform_roles" {
  for_each = var.terraform_roles
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.terraform_sa.email}"
}

resource "google_project_iam_member" "cloud_run_service_roles" {
  for_each = var.cloud_run_service_roles
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.cloud_run_service_sa.email}"
}

resource "google_project_iam_member" "github_actions_roles" {
  for_each = var.github_actions_roles
  project  = var.project_id
  role     = each.value
  member   = "serviceAccount:${google_service_account.github_actions_sa.email}"
}

# Allow GitHub Actions SA to impersonate Cloud Run service account
resource "google_service_account_iam_member" "github_actions_impersonate" {
  service_account_id = google_service_account.cloud_run_service_sa.name
  role               = "roles/iam.serviceAccountUser"
  member             = "serviceAccount:${google_service_account.github_actions_sa.email}"
}

# Service account keys (optional - you can remove these if not needed)
resource "google_service_account_key" "terraform_sa_key" {
  service_account_id = google_service_account.terraform_sa.name
}

resource "google_service_account_key" "github_actions_sa_key" {
  service_account_id = google_service_account.github_actions_sa.name
}

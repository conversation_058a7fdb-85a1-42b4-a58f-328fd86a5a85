variable "project_id" {
  description = "GCP project ID"
  type        = string
}

variable "region" {
  description = "GCP region"
  type        = string
}

variable "instance_name" {
  description = "Redis instance name"
  type        = string
}

variable "tier" {
  description = "Redis tier"
  type        = string
  default     = "STANDARD_HA"
}

variable "memory_size_gb" {
  description = "Redis memory size in GB"
  type        = number
  default     = 1
}

variable "redis_version" {
  description = "Redis version"
  type        = string
  default     = "REDIS_5_0"
}

variable "auth_enabled" {
  description = "Enable Redis AUTH"
  type        = bool
  default     = false
}

variable "transit_encryption_mode" {
  description = "Transit encryption mode"
  type        = string
  default     = "DISABLED"
}

variable "network_name" {
  description = "VPC network name"
  type        = string
  default     = "default"
}

variable "maintenance_policy" {
  description = "Maintenance policy configuration"
  type = object({
    day          = string
    start_hour   = number
    start_minute = number
  })
  default = {
    day          = "SUNDAY"
    start_hour   = 2
    start_minute = 0
  }
}

variable "api_dependencies" {
  description = "API service dependencies"
  type        = list(any)
  default     = []
}

import { defineMessages } from 'react-intl';

export const desktopTransactionTableMessages = defineMessages({
  type: {
    id: 'profile.transactionHistory.table.type',
    defaultMessage: 'Type',
  },
  amount: {
    id: 'profile.transactionHistory.table.amount',
    defaultMessage: 'Amount',
  },
  date: {
    id: 'profile.transactionHistory.table.date',
    defaultMessage: 'Date',
  },
  description: {
    id: 'profile.transactionHistory.table.description',
    defaultMessage: 'Description',
  },
});

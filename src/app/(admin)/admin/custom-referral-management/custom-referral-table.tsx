import { Edit, Trash2 } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import type { UserWithCustomReferral } from './custom-referral-types';
import { formatBpsToPercent } from './custom-referral-utils';

interface CustomReferralTableProps {
  users: UserWithCustomReferral[];
  onEditUser: (user: UserWithCustomReferral) => void;
  onRemoveUser: (userId: string) => void;
}

export const CustomReferralTable = ({
  users,
  onEditUser,
  onRemoveUser,
}: CustomReferralTableProps) => {
  if (users.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No users with custom referral fees found
      </div>
    );
  }

  return (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>User</TableHead>
            <TableHead>Telegram ID</TableHead>
            <TableHead>Referral Fee</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id}>
              <TableCell>
                <div>
                  <div className="font-medium">
                    {user.displayName || 'Unknown User'}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {user.email || user.id.slice(0, 8) + '...'}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                {user.tg_id ? (
                  <span className="font-mono text-sm">{user.tg_id}</span>
                ) : (
                  <span className="text-muted-foreground">N/A</span>
                )}
              </TableCell>
              <TableCell>
                <span className="font-medium">
                  {formatBpsToPercent(user.referral_fee)}%
                </span>
                <span className="text-sm text-muted-foreground ml-2">
                  ({user.referral_fee} BPS)
                </span>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEditUser(user)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onRemoveUser(user.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

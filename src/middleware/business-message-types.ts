export type UniqueGift = {
  gift: {
    base_name: string;
    name: string;
    number: number;
    model: {
      name: string;
      sticker: Sticker;
      rarity_per_mille: number;
    };
    symbol: {
      name: string;
      sticker: Sticker & { needs_repainting?: boolean };
      rarity_per_mille: number;
    };
    backdrop: {
      name: string;
      colors: {
        center_color: number;
        edge_color: number;
        symbol_color: number;
        text_color: number;
      };
      rarity_per_mille: number;
    };
  };
  owned_gift_id: string;
  transfer_star_count: number;
  origin: string;
};

type Sticker = {
  width: number;
  height: number;
  emoji: string;
  is_animated: boolean;
  is_video: boolean;
  type: "custom_emoji";
  custom_emoji_id: string;
  file_id: string;
  file_unique_id: string;
  file_size: number;
  thumbnail: Thumbnail;
  thumb: Thumbnail;
};

type Thumbnail = {
  file_id: string;
  file_unique_id: string;
  file_size: number;
  width: number;
  height: number;
};

export type TelegramBusinessMessageContext = {
  update_id: number;
  business_message: {
    business_connection_id: string;
    message_id: number;
    from: {
      id: number;
      is_bot: boolean;
      first_name: string;
      username: string;
      language_code: string;
      is_premium: boolean;
    };
    chat: {
      id: number;
      first_name: string;
      username: string;
      type: "private" | "channel" | "supergroup" | "group";
    };
    date: number;
    unique_gift: UniqueGift;
  };
};

variable "project_id" {
  description = "GCP project ID"
  type        = string
}

variable "region" {
  description = "GCP region"
  type        = string
}

variable "repository_id" {
  description = "Artifact Registry repository ID"
  type        = string
}

variable "description" {
  description = "Repository description"
  type        = string
}

variable "api_dependencies" {
  description = "API service dependencies"
  type        = list(any)
  default     = []
}

variable "immutable_tags" {
  description = "Immutable Docker tags"
  type        = bool
  default     = false
}
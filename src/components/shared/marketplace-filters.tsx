'use client';

import { Input as TgInput, Select } from '@telegram-apps/telegram-ui';
import { useIntl } from 'react-intl';
import { useLocalStorage } from 'usehooks-ts';

import { CollectionSelect } from '@/components/ui/collection-select';
import { useOrderListFilters } from '@/contexts/OrderListFiltersContext';
import { LocalStorageKeys } from '@/core.constants';

import { marketplaceFiltersMessages } from './marketplace-filters.messages';

export const MarketplaceFilters = () => {
  const { formatMessage: t } = useIntl();
  const {
    filters,
    collections,
    setMinPrice,
    setMaxPrice,
    setSelectedCollection,
    setSortBy,
  } = useOrderListFilters();
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );

  const handleMinPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMinPrice(value);
  };

  const handleMaxPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMaxPrice(value);
  };

  return (
    <div className="flex flex-wrap items-end gap-2 py-3 rounded-lg">
      <div className="flex-1 min-w-[130px]">
        <div className="[&>div]:p-0!">
          <TgInput
            type="number"
            header={t(marketplaceFiltersMessages.min)}
            placeholder="0"
            value={filters.minPrice}
            onChange={handleMinPriceChange}
            min="0"
            step="0.01"
            className="text-white text-sm h-9 [&+h6]:-top-[12px]! [&+h6]:max-w-[100px]!"
          />
        </div>
      </div>

      <div className="flex-1 min-w-[130px]">
        <div className="[&>div]:p-0!">
          <TgInput
            type="number"
            header={t(marketplaceFiltersMessages.max)}
            placeholder="0"
            value={filters.maxPrice}
            onChange={handleMaxPriceChange}
            min="0"
            step="0.01"
            className="text-white text-sm h-9 [&+h6]:-top-[12px]!"
          />
        </div>
      </div>

      <div className="flex-1 min-w-[130px] mt-2">
        <CollectionSelect
          animated={isAnimatedCollection}
          collections={collections}
          value={filters.selectedCollection}
          onValueChange={setSelectedCollection}
          placeholder={t(marketplaceFiltersMessages.allCollections)}
        />
      </div>

      <div className="flex-1 min-w-[130px] mt-2">
        <div className="[&>div]:p-0!">
          <Select
            header={t(marketplaceFiltersMessages.sortBy)}
            value={filters.sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="[&+h6]:max-w-[90%]! [&>select]:px-[12px]! [&>select]:py-[6px]! [&+h6]:-top-[12px]! [&>select]:bg-[#17212b]! [&>select]:text-[#f5f5f5]! [&>select]:border-[#2a3441]! [&>select]:appearance-none! [&>select]:forced-color-adjust-none!"
          >
            <option
              value="price_desc"
              className="bg-[#2a3441] text-[#f5f5f5] forced-color-adjust-none"
            >
              {t(marketplaceFiltersMessages.priceHighToLow)}
            </option>
            <option
              value="price_asc"
              className="bg-[#2a3441] text-[#f5f5f5] forced-color-adjust-none"
            >
              {t(marketplaceFiltersMessages.priceLowToHigh)}
            </option>
            <option
              value="date_desc"
              className="bg-[#2a3441] text-[#f5f5f5] forced-color-adjust-none"
            >
              {t(marketplaceFiltersMessages.newestFirst)}
            </option>
            <option
              value="date_asc"
              className="bg-[#2a3441] text-[#f5f5f5] forced-color-adjust-none"
            >
              {t(marketplaceFiltersMessages.oldestFirst)}
            </option>
          </Select>
        </div>
      </div>
    </div>
  );
};

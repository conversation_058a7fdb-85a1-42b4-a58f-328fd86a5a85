import type { CollectionEntity } from '@/core.constants';

interface CollectionNameProps {
  collection: CollectionEntity | null | undefined;
  className?: string;
  fallback?: string;
}

export function CollectionName({
  collection,
  className = '',
  fallback,
}: CollectionNameProps) {
  const defaultFallback = fallback;

  return (
    <span className={className}>{collection?.name || defaultFallback}</span>
  );
}

import { defineMessages } from 'react-intl';

export const loginModalMessages = defineMessages({
  authenticationRequired: {
    id: 'loginModal.authenticationRequired',
    defaultMessage: 'Authentication Required',
  },
  mustBeLoggedIn: {
    id: 'loginModal.mustBeLoggedIn',
    defaultMessage: 'You must be logged in to perform this action.',
  },
  signingIn: {
    id: 'loginModal.signingIn',
    defaultMessage: 'Signing in...',
  },
  signInWithTelegram: {
    id: 'loginModal.signInWithTelegram',
    defaultMessage: 'Sign in with Telegram',
  },
});

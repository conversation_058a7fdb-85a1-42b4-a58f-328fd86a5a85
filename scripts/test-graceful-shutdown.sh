#!/bin/bash

# Test script for graceful shutdown behavior
# This script helps test the SIGTERM handling locally

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Graceful Shutdown${NC}"
echo "=================================="

# Check if bot is already running
if pgrep -f "bun.*start" > /dev/null; then
    echo -e "${YELLOW}⚠️ Bot process already running, stopping it first...${NC}"
    pkill -f "bun.*start" || true
    sleep 2
fi

# Start the bot in background
echo -e "${BLUE}🚀 Starting bot in background...${NC}"
bun run start &
BOT_PID=$!

echo "Bot PID: $BOT_PID"

# Wait for bot to start up
echo -e "${BLUE}⏳ Waiting for bot to start up...${NC}"
sleep 5

# Check if bot is running
if ! kill -0 $BOT_PID 2>/dev/null; then
    echo -e "${RED}❌ Bot failed to start${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Bot started successfully${NC}"

# Test healthcheck endpoint
echo -e "${BLUE}🔍 Testing healthcheck endpoint...${NC}"
if curl -s http://localhost:8080/healthcheck > /dev/null; then
    echo -e "${GREEN}✅ Healthcheck endpoint responding${NC}"
else
    echo -e "${YELLOW}⚠️ Healthcheck endpoint not responding${NC}"
fi

# Send SIGTERM and measure shutdown time
echo -e "${BLUE}🛑 Sending SIGTERM to test graceful shutdown...${NC}"
start_time=$(date +%s)

kill -TERM $BOT_PID

# Wait for process to exit
wait $BOT_PID 2>/dev/null || true

end_time=$(date +%s)
shutdown_time=$((end_time - start_time))

echo -e "${GREEN}✅ Bot shut down gracefully in ${shutdown_time} seconds${NC}"

if [ $shutdown_time -le 8 ]; then
    echo -e "${GREEN}✅ Shutdown time is within Cloud Run limits (≤8s)${NC}"
else
    echo -e "${RED}❌ Shutdown time exceeds Cloud Run limits (>8s)${NC}"
    exit 1
fi

echo -e "${GREEN}🎉 Graceful shutdown test completed successfully!${NC}"

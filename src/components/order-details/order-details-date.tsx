'use client';

import { format } from 'date-fns';
import { enUS, ru } from 'date-fns/locale';
import type { Timestamp } from 'firebase/firestore';
import { useIntl } from 'react-intl';

import { AppLocale, DATE_FORMAT_TRANSACTION } from '@/core.constants';
import { firebaseTimestampToDate } from '@/lib/utils';
import { useRootContext } from '@/root-context';

import { orderDetailsDateMessages } from './intl/order-details-date.messages';

interface OrderDetailsDateProps {
  updatedAt?: Date | Timestamp;
  className?: string;
}

const localeMap = {
  [AppLocale.en]: enUS,
  [AppLocale.ru]: ru,
};

export function OrderDetailsDate({
  updatedAt,
  className,
}: OrderDetailsDateProps) {
  const { formatMessage: t } = useIntl();
  const { locale } = useRootContext();

  if (!updatedAt) {
    return null;
  }

  const date = firebaseTimestampToDate(updatedAt);
  const dateLocale = localeMap[locale] || enUS;

  const formattedDate = format(date, DATE_FORMAT_TRANSACTION, {
    locale: dateLocale,
  });

  return (
    <div className={className}>
      <div className="flex justify-between items-center py-2">
        <span className="text-[#708499] text-sm">
          {t(orderDetailsDateMessages.lastUpdate)}
        </span>
        <span className="text-[#f5f5f5] text-sm font-medium">
          {formattedDate}
        </span>
      </div>
    </div>
  );
}

# Webhook Setup and Troubleshooting

This document explains how to properly configure webhooks for the Telegram bot to prevent SIGTERM issues and ensure reliable operation on Cloud Run.

## Problem

When Cloud Run containers restart (which triggers SIGTERM signals), the Telegram bot loses its webhook configuration. This causes the bot to stop responding to messages until the webhook is re-established.

## Solution

The bot now automatically sets up webhooks on startup when running in production mode with proper environment variables.

## Environment Variables

### Required for Production

- `NODE_ENV=production` - Enables webhook mode
- `WEBHOOK_URL` - Your Cloud Run service URL (must use HTTPS)
- `BOT_TOKEN` - Your Telegram bot token
- `PORT` - Port for the HTTP server (defaults to 8080)

### Example

```env
NODE_ENV=production
WEBHOOK_URL=https://your-cloud-run-service.run.app
BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
PORT=8080
```

## How It Works

1. **Startup**: <PERSON><PERSON> automatically sets webhook to `${WEBHOOK_URL}/webhook`
2. **Webhook Handling**: HTTP server processes incoming updates at `/webhook` endpoint
3. **Graceful Shutdown**: On SIGTERM/SIGINT, webhook is cleaned up properly
4. **Restart**: New container instance re-establishes webhook automatically

## Webhook Configuration

The bot sets up webhooks with these optimizations:

- **Max Connections**: 40 (optimal for Cloud Run)
- **Allowed Updates**: Only essential update types to reduce load
- **Drop Pending Updates**: Clears old updates on restart
- **Error Handling**: Comprehensive logging and error recovery

## Testing

### Test Webhook Status

```bash
npm run test:webhook
```

This will show:
- Current webhook URL
- Pending update count
- Last error (if any)
- Configuration details

### Test HTTP Server

```bash
npm run test:http
```

### Test Health Check

```bash
npm run test:healthcheck
```

## Troubleshooting

### Bot Not Responding After Restart

1. Check webhook status:
   ```bash
   npm run test:webhook
   ```

2. Verify environment variables:
   ```bash
   echo $WEBHOOK_URL
   echo $NODE_ENV
   ```

3. Check logs for webhook setup errors:
   ```bash
   # Look for these log messages:
   # ✅ Webhook set to: https://...
   # 📡 Webhook info: {...}
   ```

### Common Issues

#### 1. WEBHOOK_URL Not Set
```
❌ WEBHOOK_URL is required for production deployment
```
**Solution**: Set `WEBHOOK_URL` environment variable

#### 2. HTTP Instead of HTTPS
```
❌ WEBHOOK_URL must use HTTPS in production
```
**Solution**: Use HTTPS URL for webhook

#### 3. Webhook URL Mismatch
```
❌ Webhook URL mismatch. Expected: ..., Got: ...
```
**Solution**: Check if another bot instance is running or webhook was set elsewhere

#### 4. Pending Updates
```
⚠️ Warning: 5 pending updates found
```
**Solution**: Normal after restart, updates will be processed

### Manual Webhook Management

If you need to manually manage webhooks:

```bash
# Clear webhook
curl -X POST "https://api.telegram.org/bot${BOT_TOKEN}/deleteWebhook"

# Set webhook manually
curl -X POST "https://api.telegram.org/bot${BOT_TOKEN}/setWebhook" \
  -d "url=${WEBHOOK_URL}/webhook" \
  -d "max_connections=40"

# Check webhook status
curl "https://api.telegram.org/bot${BOT_TOKEN}/getWebhookInfo"
```

## Cloud Run Deployment

### Dockerfile Considerations

Ensure your Dockerfile:
1. Exposes the correct port
2. Sets proper environment variables
3. Handles graceful shutdown

### Service Configuration

Cloud Run service should:
- Allow unauthenticated requests (for webhook)
- Set minimum instances to 1 (for immediate response)
- Configure proper health checks

### Health Checks

The bot provides health check endpoints:
- `GET /healthcheck` - Detailed health status
- `GET /` - Basic service info

## Monitoring

Monitor these metrics:
- Webhook error count
- Pending update count
- Response time to webhook requests
- Container restart frequency

## Best Practices

1. **Always use HTTPS** for webhook URLs
2. **Set minimum instances** to 1 in Cloud Run
3. **Monitor webhook errors** in logs
4. **Test webhook setup** after deployment
5. **Use health checks** for monitoring

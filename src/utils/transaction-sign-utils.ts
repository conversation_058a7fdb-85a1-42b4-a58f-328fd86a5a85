/**
 * Standardized transaction amount sign rules:
 * - DEPOSIT: Always negative (-) - money leaving user's wallet to platform
 * - WITHDRAW: Always positive (+) - money returning to user's wallet from platform
 * - BUY_LOCK_COLLATERAL: Always negative (-) - buyer's collateral locked when creating buy order
 * - UNLOCK_COLLATERAL: Always positive (+) - collateral returned to user
 * - SELL_LOCK_COLLATERAL: Always negative (-) - seller's collateral locked when creating sell order
 * - REFERRAL_FEE: Always positive (+) - earnings received by referrer
 * - CANCELATION_FEE: Context-dependent sign:
 *   - Positive (+) when receiving compensation for counterparty cancellation
 *   - Negative (-) when paying penalty for own cancellation
 * - REFUND: Always positive (+) - money returned to user
 * - SELL_FULFILLMENT: Always positive (+) - seller receiving buyer's payment upon order completion
 * - RESELL_FEE_EARNINGS: Always positive (+) - earnings from reselling an order
 */

import type { TxType } from '@/core.constants';

export function applyTransactionSign(
  amount: number,
  txType: TxType,
  isReceivingCompensation?: boolean,
): number {
  if (amount < 0) {
    throw new Error('Amount must be positive when applying transaction sign');
  }

  switch (txType) {
    case 'deposit':
    case 'buy_lock_collateral':
    case 'sell_lock_collateral':
      return -amount; // Money leaving user

    case 'withdraw':
    case 'unlock_collateral':
    case 'referral_fee':
    case 'refund':
    case 'sell_fulfillment':
    case 'resell_fee_earnings':
      return amount; // Money coming to user

    case 'cancelation_fee':
      if (isReceivingCompensation === undefined) {
        throw new Error(
          'isReceivingCompensation must be specified for CANCELATION_FEE transactions',
        );
      }
      return isReceivingCompensation ? amount : -amount;

    default:
      throw new Error(`Unknown transaction type: ${txType}`);
  }
}

export function getTransactionAmountColor(amount: number): string {
  return amount >= 0 ? 'text-green-600' : 'text-red-600';
}

export function formatTransactionAmount(amount: number): string {
  const sign = amount >= 0 ? '+' : '-';
  const absoluteAmount = Math.abs(amount);
  return `${sign}${absoluteAmount.toFixed(2)} TON`;
}

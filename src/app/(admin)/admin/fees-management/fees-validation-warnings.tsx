import { AlertTriangle } from 'lucide-react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface FeesValidationWarningsProps {
  warnings: string[];
}

export const FeesValidationWarnings = ({
  warnings,
}: FeesValidationWarningsProps) => {
  if (warnings.length === 0) {
    return null;
  }

  return (
    <Alert variant="destructive" className="mb-6">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Configuration Warnings</AlertTitle>
      <AlertDescription>
        <ul className="list-disc list-inside space-y-1">
          {warnings.map((warning) => (
            <li key={warning} className="text-sm">
              {warning}
            </li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  );
};

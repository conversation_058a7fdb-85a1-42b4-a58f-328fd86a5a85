import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const host = request.headers.get('host') || 'localhost:3001';

  // Determine if this is a development environment
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isLocalhost =
    host.includes('localhost') ||
    host.includes('127.0.0.1') ||
    host.includes('192.168') ||
    host.includes('10.');

  // Use HTTP for local development, HTTPS for production
  const protocol = isDevelopment && isLocalhost ? 'http' : 'https';
  const baseUrl = `${protocol}://${host}`;

  const manifest = {
    url: baseUrl,
    name: isDevelopment ? 'Marketplace UI (Dev)' : 'Marketplace UI',
    iconUrl: `${baseUrl}/ton.svg`,
  };

  return NextResponse.json(manifest, {
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type',
      // Cache for 1 hour in production, 1 minute in development
      'Cache-Control': isDevelopment ? 'max-age=60' : 'max-age=3600',
    },
  });
}

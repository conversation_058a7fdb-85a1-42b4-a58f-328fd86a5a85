data "google_compute_network" "default" {
  name    = var.network_name
  project = var.project_id
}

resource "google_redis_instance" "cache" {
  name           = var.instance_name
  tier           = var.tier
  memory_size_gb = var.memory_size_gb
  region         = var.region
  redis_version  = var.redis_version
  project        = var.project_id
  
  auth_enabled            = var.auth_enabled
  transit_encryption_mode = var.transit_encryption_mode
  
  authorized_network = data.google_compute_network.default.id
  
  dynamic "maintenance_policy" {
    for_each = var.maintenance_policy != null ? [var.maintenance_policy] : []
    content {
      weekly_maintenance_window {
        day = maintenance_policy.value.day
        start_time {
          hours   = maintenance_policy.value.start_hour
          minutes = maintenance_policy.value.start_minute
        }
      }
    }
  }
  
  depends_on = [var.api_dependencies]
}

import { z } from 'zod';

export const customReferralSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  referralFee: z.number().min(0, 'Referral fee must be non-negative'),
});

export const editReferralSchema = z.object({
  referralFee: z.number().min(0).max(10000),
});

export type CustomReferralFormData = z.infer<typeof customReferralSchema>;
export type EditReferralFormData = z.infer<typeof editReferralSchema>;

export interface UserWithCustomReferral {
  id: string;
  displayName?: string | null;
  email?: string | null;
  tg_id?: string;
  telegram_handle?: string;
  referral_fee: number;
}

#!/bin/bash

# Webhook Setup Script for Marketplace Bot
# This script helps set up and verify webhook configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Load environment variables
if [ -f .env.production ]; then
    export $(cat .env.production | grep -v '^#' | xargs)
fi

echo -e "${BLUE}🤖 Marketplace Bot Webhook Setup${NC}"
echo "=================================="

# Check required environment variables
if [ -z "$BOT_TOKEN" ]; then
    echo -e "${RED}❌ BOT_TOKEN is not set${NC}"
    exit 1
fi

if [ -z "$WEBHOOK_URL" ]; then
    echo -e "${RED}❌ WEBHOOK_URL is not set${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Environment variables loaded${NC}"
echo "Bot Token: ${BOT_TOKEN:0:10}..."
echo "Webhook URL: $WEBHOOK_URL"
echo ""

# Function to make Telegram API calls
telegram_api() {
    local method=$1
    local data=$2
    local url="https://api.telegram.org/bot${BOT_TOKEN}/${method}"
    
    if [ -n "$data" ]; then
        curl -s -X POST "$url" -d "$data"
    else
        curl -s "$url"
    fi
}

# Get current webhook info
echo -e "${BLUE}📡 Getting current webhook info...${NC}"
webhook_info=$(telegram_api "getWebhookInfo")
echo "$webhook_info" | jq '.'

# Extract current webhook URL
current_url=$(echo "$webhook_info" | jq -r '.result.url // empty')
expected_url="${WEBHOOK_URL}/webhook"

echo ""
echo -e "${BLUE}🔍 Webhook Status Check${NC}"
echo "Expected URL: $expected_url"
echo "Current URL:  $current_url"

if [ "$current_url" = "$expected_url" ]; then
    echo -e "${GREEN}✅ Webhook is correctly configured${NC}"
else
    echo -e "${YELLOW}⚠️ Webhook needs to be updated${NC}"
    
    # Ask user if they want to update
    read -p "Do you want to set the webhook now? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}🔧 Setting webhook...${NC}"
        
        # Delete existing webhook
        echo "Deleting existing webhook..."
        delete_result=$(telegram_api "deleteWebhook" "drop_pending_updates=true")
        echo "$delete_result" | jq '.'
        
        # Wait a moment
        sleep 2
        
        # Set new webhook
        echo "Setting new webhook..."
        set_result=$(telegram_api "setWebhook" "url=${expected_url}&max_connections=40")
        echo "$set_result" | jq '.'
        
        # Verify
        echo "Verifying webhook..."
        sleep 1
        new_webhook_info=$(telegram_api "getWebhookInfo")
        echo "$new_webhook_info" | jq '.'
        
        new_url=$(echo "$new_webhook_info" | jq -r '.result.url // empty')
        if [ "$new_url" = "$expected_url" ]; then
            echo -e "${GREEN}✅ Webhook successfully set!${NC}"
        else
            echo -e "${RED}❌ Failed to set webhook${NC}"
            exit 1
        fi
    fi
fi

# Check for errors
last_error_date=$(echo "$webhook_info" | jq -r '.result.last_error_date // empty')
if [ -n "$last_error_date" ] && [ "$last_error_date" != "null" ]; then
    last_error_message=$(echo "$webhook_info" | jq -r '.result.last_error_message // empty')
    error_date=$(date -d "@$last_error_date" 2>/dev/null || date -r "$last_error_date" 2>/dev/null || echo "Unknown")
    echo -e "${YELLOW}⚠️ Last webhook error: $last_error_message at $error_date${NC}"
else
    echo -e "${GREEN}✅ No webhook errors detected${NC}"
fi

# Check pending updates
pending_count=$(echo "$webhook_info" | jq -r '.result.pending_update_count // 0')
if [ "$pending_count" -gt 0 ]; then
    echo -e "${YELLOW}⚠️ $pending_count pending updates found${NC}"
else
    echo -e "${GREEN}✅ No pending updates${NC}"
fi

echo ""
echo -e "${BLUE}🌐 Testing webhook endpoint...${NC}"

# Test if webhook endpoint is accessible
if command -v curl >/dev/null 2>&1; then
    response=$(curl -s -o /dev/null -w "%{http_code}" "$expected_url" || echo "000")
    if [ "$response" = "404" ] || [ "$response" = "405" ]; then
        echo -e "${GREEN}✅ Webhook endpoint is accessible (HTTP $response)${NC}"
    elif [ "$response" = "000" ]; then
        echo -e "${RED}❌ Webhook endpoint is not accessible${NC}"
    else
        echo -e "${YELLOW}⚠️ Webhook endpoint returned HTTP $response${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ curl not available, skipping endpoint test${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Webhook setup complete!${NC}"
echo ""
echo "Next steps:"
echo "1. Deploy your bot to Cloud Run"
echo "2. Set NODE_ENV=production in Cloud Run environment"
echo "3. Monitor logs for webhook setup messages"
echo "4. Test bot functionality"
echo ""
echo "Troubleshooting:"
echo "- Run 'npm run test:webhook' to check status"
echo "- Check Cloud Run logs for errors"
echo "- Verify WEBHOOK_URL is accessible from internet"

export const formatBpsToPercent = (bps: number): string => {
  return (bps / 100).toFixed(2);
};

export const getCustomReferralWarnings = (
  referralFee: number,
  maxPurchaseFee: number,
  maxResellPurchaseFee: number,
): string[] => {
  const warnings: string[] = [];

  if (referralFee > maxPurchaseFee) {
    warnings.push(
      `Custom referral fee (${formatBpsToPercent(referralFee)}%) exceeds purchase fee (${formatBpsToPercent(maxPurchaseFee)}%)`,
    );
  }

  if (referralFee > maxResellPurchaseFee) {
    warnings.push(
      `Custom referral fee (${formatBpsToPercent(referralFee)}%) exceeds resell purchase fee (${formatBpsToPercent(maxResellPurchaseFee)}%)`,
    );
  }

  return warnings;
};

// This file is normally used for setting up analytics and other
// services that require one-time initialization on the client.

import { isTMA, retrieveLaunchParams } from '@telegram-apps/sdk-react';

import { init } from './init';
import { mockEnv } from './mockEnv';

// Only initialize Telegram SDK if we're in a Telegram environment or development mode
// This prevents errors when accessing admin/auth pages outside of Telegram
const shouldInitializeTelegram = () => {
  if (typeof window === 'undefined') return false;

  // Check if we're on admin or auth pages that should work outside Telegram
  const pathname = window.location.pathname;
  const isAdminOrAuth =
    pathname.startsWith('/admin') || pathname.startsWith('/auth');

  // For admin/auth pages, only initialize if we're in development mode
  if (isAdminOrAuth) {
    return process.env.NODE_ENV === 'development';
  }

  // For other pages, always try to initialize
  return true;
};

if (shouldInitializeTelegram()) {
  mockEnv()
    .then(() => {
      // Check if we're actually in a Telegram environment
      isTMA('complete')
        .then((isTelegramApp) => {
          if (isTelegramApp || process.env.NODE_ENV === 'development') {
            const launchParams = retrieveLaunchParams();
            const { tgWebAppPlatform: platform } = launchParams;
            const debug =
              (launchParams.tgWebAppStartParam ?? '').includes('debug') ||
              process.env.NODE_ENV === 'development';

            // Configure all application dependencies.
            init({
              debug,
              eruda: debug && ['ios', 'android'].includes(platform),
              mockForMacOS: platform === 'macos',
            });

            window?.Telegram?.WebApp?.expand();
          }
        })
        .catch((e) => {
          console.log('Telegram environment check failed:', e);
        });
    })
    .catch((e) => {
      console.log('Telegram initialization failed:', e);
    });
}

import { useIntl } from 'react-intl';

import { Badge } from '@/components/ui/badge';
import type { OrderEntity, OrderStatus } from '@/core.constants';
import { OrderStatus as OrderStatusEnum } from '@/core.constants';
import { cn } from '@/lib/utils';
import { getStatusConfig } from '@/services/order-service';

import { orderStatusMessages } from './order-status-badge.messages';

interface OrderStatusBadgeProps {
  order: OrderEntity;
  variant?: 'default' | 'pill';
  className?: string;
}

interface StatusBadgeProps {
  status: OrderStatus;
  variant?: 'default' | 'pill';
  className?: string;
}

export function OrderStatusBadge({
  order,
  variant = 'default',
  className,
}: OrderStatusBadgeProps) {
  return (
    <StatusBadge
      status={order.status}
      variant={variant}
      className={className}
    />
  );
}

export function StatusBadge({
  status,
  variant = 'default',
  className,
}: StatusBadgeProps) {
  const { formatMessage: t } = useIntl();
  const statusConfig = getStatusConfig(status);

  const getStatusLabel = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatusEnum.ACTIVE:
        return t(orderStatusMessages.active);
      case OrderStatusEnum.PAID:
        return t(orderStatusMessages.paid);
      case OrderStatusEnum.GIFT_SENT_TO_RELAYER:
        return t(orderStatusMessages.giftSentToRelayer);
      case OrderStatusEnum.FULFILLED:
        return t(orderStatusMessages.fulfilled);
      case OrderStatusEnum.CANCELLED:
        return t(orderStatusMessages.cancelled);
      default:
        return statusConfig.label;
    }
  };

  const baseClasses =
    variant === 'pill' ? 'text-[10px] px-2 py-0.5 border' : '';

  return (
    <Badge
      variant="outline"
      className={cn(baseClasses, statusConfig.className, className)}
    >
      {getStatusLabel(status)}
    </Badge>
  );
}

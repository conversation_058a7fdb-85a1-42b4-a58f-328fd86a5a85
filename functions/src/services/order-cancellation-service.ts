import * as admin from "firebase-admin";
import { OrderEntity, OrderStatus, UserRole, TxType } from "../types";
import {
  spendLockedFunds,
  unlockFundsWithHistory,
  addFundsWithHistory,
} from "./balance-service";
import { createTransactionRecord } from "./transaction-history-service";
import {
  applyFeeToMarketplaceRevenue,
  applyFixedCancelOrderFee,
} from "./fee-service";
import { BPS_DIVISOR } from "../constants";
import { safeMultiply, safeDivide, safeSubtract } from "../utils";
import { log } from "../utils/logger";

async function getAdminUserIds(
  userIds: string[],
  db: admin.firestore.Firestore
) {
  if (userIds.length === 0) return new Set();

  const userDocs = await Promise.all(
    userIds.map((id) => db.collection("users").doc(id).get())
  );

  const adminIds = new Set<string>();
  userDocs.forEach((doc, index) => {
    if (doc.exists && doc.data()?.role === "admin") {
      adminIds.add(userIds[index]);
    }
  });

  return adminIds;
}

export async function processOrderCancellation(
  order: OrderEntity,
  cancellingUserId: string
) {
  const db = admin.firestore();

  const cancellingUserDoc = await db
    .collection("users")
    .doc(cancellingUserId)
    .get();
  const cancellingUser = cancellingUserDoc.exists
    ? cancellingUserDoc.data()
    : null;
  const isAdminCancelling = cancellingUser?.role === "admin";

  const hasBothParticipants = Boolean(order.buyerId && order.sellerId);
  const isActiveSinglePersonOrder =
    order.status === OrderStatus.ACTIVE && !hasBothParticipants;
  const isPaidTwoPersonOrder =
    order.status === OrderStatus.PAID && hasBothParticipants;

  if (isActiveSinglePersonOrder) {
    return await processSinglePersonCancellation({
      order,
      cancellingUserId,
      db,
      isAdminCancelling,
    });
  } else if (isPaidTwoPersonOrder) {
    return await processTwoPersonCancellation({
      order,
      cancellingUserId,
      db,
      isAdminCancelling,
    });
  } else {
    throw new Error(
      `Order ${order.id} is not in a valid state for cancellation`
    );
  }
}

async function processSinglePersonCancellation(params: {
  order: OrderEntity;
  cancellingUserId: string;
  db: admin.firestore.Firestore;
  isAdminCancelling: boolean;
}) {
  const { order, cancellingUserId, db, isAdminCancelling } = params;
  const buyerLockPercentageBPS = order.fees?.buyer_locked_percentage ?? 0;
  const sellerLockPercentageBPS = order.fees?.seller_locked_percentage ?? 0;
  const buyerLockPercentage = buyerLockPercentageBPS / 10000; // Convert BPS to decimal
  const sellerLockPercentage = sellerLockPercentageBPS / 10000; // Convert BPS to decimal

  const buyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

  if (isAdminCancelling) {
    await db.collection("orders").doc(order.id).update({
      status: OrderStatus.CANCELLED,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message:
        "Order cancelled by admin. All locked collateral has been released without penalties.",
      feeApplied: 0,
      feeType: "none",
    };
  }

  if (order.buyerId === cancellingUserId) {
    await unlockFundsWithHistory({
      userId: order.buyerId,
      amount: buyerLockedAmount,
      txType: TxType.UNLOCK_COLLATERAL,
      orderId: order.id,
      description: `Collateral unlocked due to order cancellation (${buyerLockedAmount} TON)`,
    });
  } else if (order.sellerId === cancellingUserId) {
    await unlockFundsWithHistory({
      userId: order.sellerId,
      amount: sellerLockedAmount,
      txType: TxType.UNLOCK_COLLATERAL,
      orderId: order.id,
      description: `Collateral unlocked due to order cancellation (${sellerLockedAmount} TON)`,
    });
  }

  const feeApplied = isAdminCancelling
    ? 0
    : await applyFixedCancelOrderFee(cancellingUserId);

  await db.collection("orders").doc(order.id).update({
    status: OrderStatus.CANCELLED,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  const feeMessage =
    feeApplied > 0
      ? ` A cancellation fee of ${feeApplied} TON was applied.`
      : "";

  const adminMessage = isAdminCancelling
    ? " (Admin cancellation - no fees applied)"
    : "";

  return {
    success: true,
    message: `Order cancelled successfully. Locked funds have been released.${feeMessage}${adminMessage}`,
    feeApplied,
    feeType: "fixed",
  };
}

async function processTwoPersonCancellation(params: {
  order: OrderEntity;
  cancellingUserId: string;
  db: admin.firestore.Firestore;
  isAdminCancelling: boolean;
}) {
  const { order, cancellingUserId, db, isAdminCancelling } = params;
  const cancelFeePercentageBPS = order.fees?.order_cancellation_fee ?? 0;
  const sellerLockPercentageBPS = order.fees?.seller_locked_percentage ?? 0;
  const cancelFeePercentage = cancelFeePercentageBPS / 10000; // Convert BPS to decimal
  const sellerLockPercentage = sellerLockPercentageBPS / 10000; // Convert BPS to decimal

  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

  const userIds = [order.buyerId, order.sellerId].filter(Boolean) as string[];
  const adminUserIds = await getAdminUserIds(userIds, db);

  // If admin is cancelling, no fees are applied - just unlock collateral
  if (isAdminCancelling) {
    // Unlock collateral for both parties without any penalties
    // Skip unlocking funds for the admin if they are either buyer or seller
    // Also skip unlocking funds if buyer or seller is admin
    if (
      order.buyerId &&
      order.buyerId !== cancellingUserId &&
      !adminUserIds.has(order.buyerId)
    ) {
      await unlockFundsWithHistory({
        userId: order.buyerId,
        amount: order.price,
        txType: TxType.UNLOCK_COLLATERAL,
        orderId: order.id,
        description: `Collateral unlocked due to admin cancellation (${order.price} TON)`,
      });
    }
    if (
      order.sellerId &&
      order.sellerId !== cancellingUserId &&
      !adminUserIds.has(order.sellerId)
    ) {
      await unlockFundsWithHistory({
        userId: order.sellerId,
        amount: sellerLockedAmount,
        txType: TxType.UNLOCK_COLLATERAL,
        orderId: order.id,
        description: `Collateral unlocked due to admin cancellation (${sellerLockedAmount} TON)`,
      });
    }

    // Update order status to cancelled
    await db.collection("orders").doc(order.id).update({
      status: OrderStatus.CANCELLED,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return {
      success: true,
      message:
        "Order cancelled by admin. All locked collateral has been released without penalties.",
      feeApplied: 0,
      feeType: "none",
    };
  }

  // Normal cancellation logic for non-admin users
  const marketplaceFee = safeDivide(
    safeMultiply(order.price, cancelFeePercentage),
    BPS_DIVISOR
  );
  const compensationAmount = safeSubtract(order.price, marketplaceFee);

  // Determine who is cancelling and apply appropriate logic
  if (cancellingUserId === order.sellerId) {
    // Seller cancels: seller loses locked amount, buyer gets compensation
    await spendLockedFunds(order.sellerId, sellerLockedAmount);

    // Record penalty transaction for the cancelling seller
    await createTransactionRecord({
      userId: order.sellerId,
      txType: TxType.CANCELATION_FEE,
      amount: sellerLockedAmount,
      orderId: order.id,
      description: `Cancellation penalty for seller (${sellerLockedAmount} TON)`,
      isReceivingCompensation: false,
    });

    await addFundsWithHistory({
      userId: order.buyerId!,
      amount: compensationAmount,
      txType: TxType.CANCELATION_FEE,
      orderId: order.id,
      description: `Cancellation compensation from seller (${compensationAmount} TON)`,
      isReceivingCompensation: true,
    });
  } else if (cancellingUserId === order.buyerId) {
    // Buyer cancels: buyer loses their payment, seller gets compensation
    await spendLockedFunds(order.buyerId, order.price);

    // Record penalty transaction for the cancelling buyer
    await createTransactionRecord({
      userId: order.buyerId,
      txType: TxType.CANCELATION_FEE,
      amount: order.price,
      orderId: order.id,
      description: `Cancellation penalty for buyer (${order.price} TON)`,
      isReceivingCompensation: false,
    });

    await addFundsWithHistory({
      userId: order.sellerId!,
      amount: compensationAmount,
      txType: TxType.CANCELATION_FEE,
      orderId: order.id,
      description: `Cancellation compensation from buyer (${compensationAmount} TON)`,
      isReceivingCompensation: true,
    });
    // Also unlock seller's locked amount
    await unlockFundsWithHistory({
      userId: order.sellerId!,
      amount: sellerLockedAmount,
      txType: TxType.UNLOCK_COLLATERAL,
      orderId: order.id,
      description: `Collateral unlocked due to buyer cancellation (${sellerLockedAmount} TON)`,
    });
  }

  // Apply marketplace fee
  if (marketplaceFee > 0) {
    await applyFeeToMarketplaceRevenue({
      feeAmount: marketplaceFee,
      feeType: "cancel_order_penalty",
    });
  }

  // Handle reseller earnings for seller
  const resellerEarnings = order.reseller_earnings_for_seller ?? 0;
  if (resellerEarnings > 0) {
    if (cancellingUserId === order.sellerId) {
      // Seller cancels: reseller earnings go to marketplace revenue
      await applyFeeToMarketplaceRevenue({
        feeAmount: resellerEarnings,
        feeType: "reseller_earnings_seller_cancel",
      });
      log.feeLog(
        "Reseller earnings added to marketplace revenue (seller cancelled)",
        {
          feeAmount: resellerEarnings,
          feeType: "reseller_earnings_seller_cancel",
          orderId: order.id,
        }
      );
    } else if (cancellingUserId === order.buyerId) {
      // Buyer cancels: reseller earnings go to seller
      await addFundsWithHistory({
        userId: order.sellerId!,
        amount: resellerEarnings,
        txType: TxType.RESELL_FEE_EARNINGS,
        orderId: order.id,
        description: `Resell fee earnings from buyer cancellation (${resellerEarnings} TON)`,
      });
      log.balanceLog("Reseller earnings added to seller (buyer cancelled)", {
        userId: order.sellerId!,
        amount: resellerEarnings,
        operation: "reseller_earnings_buyer_cancel",
        orderId: order.id,
      });
    }
  }

  // Update order status to cancelled
  await db.collection("orders").doc(order.id).update({
    status: OrderStatus.CANCELLED,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  const cancellerRole =
    cancellingUserId === order.sellerId ? UserRole.SELLER : UserRole.BUYER;
  const compensatedRole =
    cancellingUserId === order.sellerId ? UserRole.BUYER : UserRole.SELLER;

  return {
    success: true,
    message: `Order cancelled by ${cancellerRole}. ${compensatedRole} received ${compensationAmount} TON compensation. Marketplace fee: ${marketplaceFee} TON.`,
    feeApplied: marketplaceFee,
    feeType: "dynamic",
  };
}

export async function validateCancellationPermission(
  order: OrderEntity,
  userId: string
) {
  // Check if the user is an admin - admins can cancel any order
  const db = admin.firestore();
  const userDoc = await db.collection("users").doc(userId).get();
  const userData = userDoc.exists ? userDoc.data() : null;
  const isAdmin = userData?.role === "admin";

  // Skip permission validation for admin users
  if (!isAdmin) {
    if (order.buyerId !== userId && order.sellerId !== userId) {
      throw new Error(
        "You can only cancel orders where you are the buyer or seller."
      );
    }
  }

  if (order.status === OrderStatus.FULFILLED) {
    throw new Error("Cannot cancel a fulfilled order.");
  }

  if (order.status === OrderStatus.CANCELLED) {
    throw new Error("Order is already cancelled.");
  }

  if (order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    throw new Error(
      "Cannot cancel an order where the gift has already been sent to relayer."
    );
  }
}

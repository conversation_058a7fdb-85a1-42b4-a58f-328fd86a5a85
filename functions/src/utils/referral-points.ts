interface ReferralTier {
  threshold: number;
  points: number;
}

const REFERRAL_TIERS: ReferralTier[] = [
  { threshold: 3, points: 50 },
  { threshold: 5, points: 100 },
  { threshold: 10, points: 150 },
  { threshold: 25, points: 300 },
  { threshold: 50, points: 500 },
];

export function calculateReferralPoints(referralCount: number): number {
  let totalPoints = 0;

  for (const tier of REFERRAL_TIERS) {
    if (referralCount >= tier.threshold) {
      totalPoints += tier.points;
    } else {
      break;
    }
  }

  return totalPoints;
}

export function getUserPoints(
  currentUserPoints: number,
  referralCount: number
): number {
  const calculatedPoints = calculateReferralPoints(referralCount);

  return calculatedPoints;
}

export function getNextTier(referralCount: number): {
  threshold: number;
  points: number;
  remainingReferrals: number;
} | null {
  for (const tier of REFERRAL_TIERS) {
    if (referralCount < tier.threshold) {
      return {
        threshold: tier.threshold,
        points: tier.points,
        remainingReferrals: tier.threshold - referralCount,
      };
    }
  }
  return null;
}

export function getAllTiers(): ReferralTier[] {
  return REFERRAL_TIERS;
}

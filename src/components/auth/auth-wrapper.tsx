'use client';

import type { ReactElement } from 'react';
import { cloneElement, useState } from 'react';

import { useRootContext } from '@/root-context';

import { LoginModal } from './login-modal';

interface AuthWrapperProps {
  children: ReactElement;
  showModalOnClick?: boolean;
}

export function AuthWrapper({
  children,
  showModalOnClick = true,
}: AuthWrapperProps) {
  const { currentUser } = useRootContext();
  const [showLoginModal, setShowLoginModal] = useState(false);

  if (!currentUser && showModalOnClick) {
    const handleClickCapture = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setShowLoginModal(true);
    };

    return (
      <>
        {cloneElement(children, {
          onClickCapture: handleClickCapture,
          style: { cursor: 'pointer' },
        } as any)}
        <LoginModal open={showLoginModal} onOpenChange={setShowLoginModal} />
      </>
    );
  }

  return children;
}

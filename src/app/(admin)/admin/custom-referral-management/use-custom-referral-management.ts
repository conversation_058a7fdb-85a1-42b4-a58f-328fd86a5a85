import { useEffect, useState } from 'react';

import { getAppConfig } from '@/api/app-config-api';
import { getUsersWithCustomReferralFees, updateUser } from '@/api/user-api';
import { useToast } from '@/hooks/use-toast';

import type { UserWithCustomReferral } from './custom-referral-types';

export const useCustomReferralManagement = () => {
  const { toast } = useToast();
  const [usersWithCustomReferrals, setUsersWithCustomReferrals] = useState<
    UserWithCustomReferral[]
  >([]);
  const [maxPurchaseFee, setMaxPurchaseFee] = useState<number>(0);
  const [maxResellPurchaseFee, setMaxResellPurchaseFee] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);

  const loadMaxPurchaseFee = async () => {
    try {
      const config = await getAppConfig();
      setMaxPurchaseFee(config?.purchase_fee ?? 0);
      setMaxResellPurchaseFee(config?.resell_purchase_fee ?? 0);
    } catch (error) {
      console.error('Error loading purchase fee:', error);
    }
  };

  const loadUsersWithCustomReferrals = async () => {
    try {
      setIsLoading(true);
      const users = await getUsersWithCustomReferralFees();
      const usersWithCustomReferrals = users.map((user) => ({
        id: user.id,
        displayName: user.displayName,
        email: user.email,
        tg_id: user.tg_id,
        telegram_handle: user.telegram_handle,
        referral_fee: user.referral_fee ?? 0,
      }));
      setUsersWithCustomReferrals(usersWithCustomReferrals);
    } catch (error) {
      console.error('Error loading users with custom referral fees:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const removeCustomReferralFee = async (userId: string) => {
    try {
      await updateUser(userId, {
        referral_fee: undefined,
      });

      await loadUsersWithCustomReferrals();

      toast({
        title: 'Success',
        description: 'Custom referral fee removed successfully',
      });
    } catch (error) {
      console.error('Error removing custom referral fee:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove custom referral fee',
        variant: 'destructive',
      });
    }
  };

  useEffect(() => {
    loadMaxPurchaseFee();
    loadUsersWithCustomReferrals();
  }, []);

  return {
    usersWithCustomReferrals,
    maxPurchaseFee,
    maxResellPurchaseFee,
    isLoading,
    loadUsersWithCustomReferrals,
    removeCustomReferralFee,
  };
};

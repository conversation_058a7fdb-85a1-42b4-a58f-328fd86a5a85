import { CORS_CONFIG } from "./config";

export const MIN_TRANSACTION_THRESHOLD_TON = 0.9; // Minimum transaction amount to process (> 0.9 TON)

export const BPS_DIVISOR = 10000; // 1 BPS = 0.01%, so divide by 10000 to get decimal

export const MARKETPLACE_REVENUE_USER_ID = "marketplace_revenue";
export const APP_CONFIG_COLLECTION = "app_config";
export const APP_CONFIG_DOC_ID = "main";

export const MIN_REVENUE_BALANCE_ON_WALLET = 10;

export const BOT_HEALTH_CHECK_ENDPOINT = "/healthcheck";

export const commonFunctionsConfig = {
  cors: CORS_CONFIG,
  region: "europe-central2",
};

'use client';

import { Button } from '@telegram-apps/telegram-ui';

import { TonLogo } from '@/components/TonLogo';

interface PriceRowProps {
  label: string;
  amount: number;
  className?: string;
  tonLogoClassName?: string;
}

export function PriceRow({
  label,
  amount,
  className = '',
  tonLogoClassName = '',
}: PriceRowProps) {
  return (
    <div className={`flex items-center justify-between text-xs ${className}`}>
      <span>{label}</span>
      <div className="flex items-center gap-1">
        <span>{amount}</span>
        <TonLogo size={24} className={tonLogoClassName} />
      </div>
    </div>
  );
}

interface PriceButtonProps {
  amount: number;
  className?: string;
  tonLogoClassName?: string;
}

export function PriceButton({
  amount,
  className = '',
  tonLogoClassName = '',
}: PriceButtonProps) {
  return (
    <Button
      className={`w-full [&>h6]:flex [&>h6]:items-center [&>h6]:justify-center [&>h6]:gap-1 ${className}`}
    >
      {amount} <TonLogo size={24} className={`-ml-1 ${tonLogoClassName}`} />
    </Button>
  );
}

resource "google_cloud_run_service" "nodejs_app" {
  name     = var.service_name
  location = var.region
  project  = var.project_id

  template {
    spec {
      containers {
        image = var.image_url
        
        ports {
          container_port = var.container_port
        }

        resources {
          limits = {
            cpu    = var.cpu_limit
            memory = var.memory_limit
          }
        }

        dynamic "env" {
          for_each = var.environment_variables
          content {
            name  = env.key
            value = env.value
          }
        }
      }

      service_account_name = var.service_account_email
    }

    metadata {
      annotations = var.annotations
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }
}

resource "google_cloud_run_service_iam_member" "public_access" {
  location = google_cloud_run_service.nodejs_app.location
  project  = google_cloud_run_service.nodejs_app.project
  service  = google_cloud_run_service.nodejs_app.name
  role     = "roles/run.invoker"
  member   = "allUsers"
}

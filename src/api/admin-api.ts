import type { DocumentSnapshot } from 'firebase/firestore';
import {
  collection,
  deleteDoc,
  doc,
  getCountFromServer,
  getDoc,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  updateDoc,
  where,
  writeBatch,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import {
  APP_USERS_COLLECTION,
  AppCloudFunctions,
  type OrderEntity,
  ORDERS_COLLECTION_NAME,
  OrderStatus,
  type UserEntity,
} from '@/core.constants';
import { firebaseFunctions, firestore } from '@/root-context';

export interface AdminOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null;
  hasMore: boolean;
}

export interface AdminOrderStats {
  totalOrders: number;
  nonAdminOrders: {
    active: number;
    paid: number;
    giftSentToRelayer: number;
    cancelled: number;
    fulfilled: number;
  };
  adminOrders: {
    active: number;
    paid: number;
    giftSentToRelayer: number;
    cancelled: number;
    fulfilled: number;
  };
}

export const getAdmins = async (): Promise<UserEntity[]> => {
  try {
    const q = query(
      collection(firestore, APP_USERS_COLLECTION),
      where('role', '==', 'admin'),
    );

    const snapshot = await getDocs(q);
    const admins: UserEntity[] = [];

    snapshot.forEach((doc) => {
      admins.push({ id: doc.id, ...doc.data() } as UserEntity);
    });

    return admins;
  } catch (error) {
    console.error('Error getting admins:', error);
    throw error;
  }
};

const getOrderCountByStatusExcludingAdmins = async (
  status: OrderStatus,
  adminIds: string[],
): Promise<number> => {
  try {
    if (adminIds.length === 0) {
      const q = query(
        collection(firestore, ORDERS_COLLECTION_NAME),
        where('status', '==', status),
      );
      const snapshot = await getCountFromServer(q);
      return snapshot.data().count;
    }

    // Use getCountFromServer for total count and subtract admin count
    const totalSnapshot = await getCountFromServer(
      query(
        collection(firestore, ORDERS_COLLECTION_NAME),
        where('status', '==', status),
      ),
    );

    const adminCount = await getOrderCountByStatusIncludingAdmins(
      status,
      adminIds,
    );

    return totalSnapshot.data().count - adminCount;
  } catch (error) {
    console.error(
      `Error getting ${status} orders count excluding admins:`,
      error,
    );
    throw error;
  }
};

const getOrderCountByStatusIncludingAdmins = async (
  status: OrderStatus,
  adminIds: string[],
): Promise<number> => {
  try {
    if (adminIds.length === 0) return 0;

    // If we have 10 or fewer admin IDs, we can use 'in' queries with getCountFromServer
    if (adminIds.length <= 10) {
      const [buyerSnapshot, sellerSnapshot] = await Promise.all([
        getCountFromServer(
          query(
            collection(firestore, ORDERS_COLLECTION_NAME),
            where('status', '==', status),
            where('buyerId', 'in', adminIds),
          ),
        ),
        getCountFromServer(
          query(
            collection(firestore, ORDERS_COLLECTION_NAME),
            where('status', '==', status),
            where('sellerId', 'in', adminIds),
          ),
        ),
      ]);

      // Get count of orders where both buyer and seller are admins to avoid double counting
      const bothAdminSnapshot = await getCountFromServer(
        query(
          collection(firestore, ORDERS_COLLECTION_NAME),
          where('status', '==', status),
          where('buyerId', 'in', adminIds),
          where('sellerId', 'in', adminIds),
        ),
      );

      return (
        buyerSnapshot.data().count +
        sellerSnapshot.data().count -
        bothAdminSnapshot.data().count
      );
    }

    // For more than 10 admins, fall back to document fetching
    const q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      where('status', '==', status),
    );

    const snapshot = await getDocs(q);
    let count = 0;

    snapshot.forEach((doc) => {
      const orderData = doc.data() as OrderEntity;
      const buyerIsAdmin =
        orderData.buyerId && adminIds.includes(orderData.buyerId);
      const sellerIsAdmin =
        orderData.sellerId && adminIds.includes(orderData.sellerId);

      if (buyerIsAdmin || sellerIsAdmin) {
        count++;
      }
    });

    return count;
  } catch (error) {
    console.error(
      `Error getting ${status} orders count including admins:`,
      error,
    );
    throw error;
  }
};

// Individual stat functions for refresh buttons
export const getTotalOrdersCount = async (): Promise<number> => {
  const snapshot = await getCountFromServer(
    collection(firestore, ORDERS_COLLECTION_NAME),
  );
  return snapshot.data().count;
};

export const getNonAdminOrdersCount = async (): Promise<number> => {
  const admins = await getAdmins();
  const adminIds = admins.map((admin) => admin.id);

  const [active, paid, giftSent, cancelled, fulfilled] = await Promise.all([
    getOrderCountByStatusExcludingAdmins(OrderStatus.ACTIVE, adminIds),
    getOrderCountByStatusExcludingAdmins(OrderStatus.PAID, adminIds),
    getOrderCountByStatusExcludingAdmins(
      OrderStatus.GIFT_SENT_TO_RELAYER,
      adminIds,
    ),
    getOrderCountByStatusExcludingAdmins(OrderStatus.CANCELLED, adminIds),
    getOrderCountByStatusExcludingAdmins(OrderStatus.FULFILLED, adminIds),
  ]);

  return active + paid + giftSent + cancelled + fulfilled;
};

export const getAdminOrdersCount = async (): Promise<number> => {
  const admins = await getAdmins();
  const adminIds = admins.map((admin) => admin.id);

  const [active, paid, giftSent, cancelled, fulfilled] = await Promise.all([
    getOrderCountByStatusIncludingAdmins(OrderStatus.ACTIVE, adminIds),
    getOrderCountByStatusIncludingAdmins(OrderStatus.PAID, adminIds),
    getOrderCountByStatusIncludingAdmins(
      OrderStatus.GIFT_SENT_TO_RELAYER,
      adminIds,
    ),
    getOrderCountByStatusIncludingAdmins(OrderStatus.CANCELLED, adminIds),
    getOrderCountByStatusIncludingAdmins(OrderStatus.FULFILLED, adminIds),
  ]);

  return active + paid + giftSent + cancelled + fulfilled;
};

export const getNonAdminOrderCountByStatus = async (
  status: OrderStatus,
): Promise<number> => {
  const admins = await getAdmins();
  const adminIds = admins.map((admin) => admin.id);
  return getOrderCountByStatusExcludingAdmins(status, adminIds);
};

export const getAdminOrderCountByStatus = async (
  status: OrderStatus,
): Promise<number> => {
  const admins = await getAdmins();
  const adminIds = admins.map((admin) => admin.id);
  return getOrderCountByStatusIncludingAdmins(status, adminIds);
};

export const getAdminOrderStats = async (): Promise<AdminOrderStats> => {
  try {
    const admins = await getAdmins();
    const adminIds = admins.map((admin) => admin.id);

    const totalOrdersSnapshot = await getCountFromServer(
      collection(firestore, ORDERS_COLLECTION_NAME),
    );

    const [
      nonAdminActive,
      nonAdminPaid,
      nonAdminGiftSent,
      nonAdminCancelled,
      nonAdminFulfilled,
    ] = await Promise.all([
      getOrderCountByStatusExcludingAdmins(OrderStatus.ACTIVE, adminIds),
      getOrderCountByStatusExcludingAdmins(OrderStatus.PAID, adminIds),
      getOrderCountByStatusExcludingAdmins(
        OrderStatus.GIFT_SENT_TO_RELAYER,
        adminIds,
      ),
      getOrderCountByStatusExcludingAdmins(OrderStatus.CANCELLED, adminIds),
      getOrderCountByStatusExcludingAdmins(OrderStatus.FULFILLED, adminIds),
    ]);

    const [
      adminActive,
      adminPaid,
      adminGiftSent,
      adminCancelled,
      adminFulfilled,
    ] = await Promise.all([
      getOrderCountByStatusIncludingAdmins(OrderStatus.ACTIVE, adminIds),
      getOrderCountByStatusIncludingAdmins(OrderStatus.PAID, adminIds),
      getOrderCountByStatusIncludingAdmins(
        OrderStatus.GIFT_SENT_TO_RELAYER,
        adminIds,
      ),
      getOrderCountByStatusIncludingAdmins(OrderStatus.CANCELLED, adminIds),
      getOrderCountByStatusIncludingAdmins(OrderStatus.FULFILLED, adminIds),
    ]);

    return {
      totalOrders: totalOrdersSnapshot.data().count,
      nonAdminOrders: {
        active: nonAdminActive,
        paid: nonAdminPaid,
        giftSentToRelayer: nonAdminGiftSent,
        cancelled: nonAdminCancelled,
        fulfilled: nonAdminFulfilled,
      },
      adminOrders: {
        active: adminActive,
        paid: adminPaid,
        giftSentToRelayer: adminGiftSent,
        cancelled: adminCancelled,
        fulfilled: adminFulfilled,
      },
    };
  } catch (error) {
    console.error('Error getting admin order stats:', error);
    throw error;
  }
};

export const getAdminOrders = async (
  pageSize: number = 25,
  lastDoc?: DocumentSnapshot | null,
): Promise<AdminOrdersResult> => {
  try {
    const admins = await getAdmins();
    const adminIds = admins.map((admin) => admin.id);

    if (adminIds.length === 0) {
      return { orders: [], lastDoc: null, hasMore: false };
    }

    let q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      orderBy('createdAt', 'desc'),
      limit(pageSize + 1),
    );

    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    const snapshot = await getDocs(q);
    const allOrders: OrderEntity[] = [];

    snapshot.forEach((doc) => {
      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;
      const hasAdmin =
        (orderData.buyerId && adminIds.includes(orderData.buyerId)) ||
        (orderData.sellerId && adminIds.includes(orderData.sellerId));

      if (hasAdmin) {
        allOrders.push(orderData);
      }
    });

    const hasMore = allOrders.length > pageSize;
    const orders = hasMore ? allOrders.slice(0, pageSize) : allOrders;
    const newLastDoc =
      orders.length > 0 ? snapshot.docs[orders.length - 1] : null;

    return { orders, lastDoc: newLastDoc, hasMore };
  } catch (error) {
    console.error('Error getting admin orders:', error);
    throw error;
  }
};

export const getAdminOrdersPaginated = async (
  page: number = 1,
  pageSize: number = 25,
): Promise<{ orders: OrderEntity[]; totalItems: number; hasMore: boolean }> => {
  try {
    const admins = await getAdmins();
    const adminIds = admins.map((admin) => admin.id);

    if (adminIds.length === 0) {
      return { orders: [], totalItems: 0, hasMore: false };
    }

    // Get all admin orders to calculate total and paginate properly
    const q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      orderBy('createdAt', 'desc'),
    );

    const snapshot = await getDocs(q);
    const allAdminOrders: OrderEntity[] = [];

    snapshot.forEach((doc) => {
      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;
      const hasAdmin =
        (orderData.buyerId && adminIds.includes(orderData.buyerId)) ||
        (orderData.sellerId && adminIds.includes(orderData.sellerId));

      if (hasAdmin) {
        allAdminOrders.push(orderData);
      }
    });

    const totalItems = allAdminOrders.length;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const orders = allAdminOrders.slice(startIndex, endIndex);
    const hasMore = endIndex < totalItems;

    return { orders, totalItems, hasMore };
  } catch (error) {
    console.error('Error getting paginated admin orders:', error);
    throw error;
  }
};

export const deleteOrder = async (orderId: string): Promise<void> => {
  try {
    const orderRef = doc(firestore, ORDERS_COLLECTION_NAME, orderId);
    await deleteDoc(orderRef);
  } catch (error) {
    console.error('Error deleting order:', error);
    throw error;
  }
};

export const deleteAllActiveAdminOrders = async (): Promise<number> => {
  try {
    const admins = await getAdmins();
    const adminIds = admins.map((admin) => admin.id);

    if (adminIds.length === 0) {
      return 0;
    }

    const q = query(
      collection(firestore, ORDERS_COLLECTION_NAME),
      where('status', '==', OrderStatus.ACTIVE),
    );

    const snapshot = await getDocs(q);
    const activeAdminOrders: string[] = [];

    snapshot.forEach((doc) => {
      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;
      const hasAdmin =
        (orderData.buyerId && adminIds.includes(orderData.buyerId)) ||
        (orderData.sellerId && adminIds.includes(orderData.sellerId));

      if (hasAdmin) {
        activeAdminOrders.push(doc.id);
      }
    });

    // Delete all active admin orders
    const deletePromises = activeAdminOrders.map((orderId) =>
      deleteOrder(orderId),
    );
    await Promise.all(deletePromises);

    return activeAdminOrders.length;
  } catch (error) {
    console.error('Error deleting all active admin orders:', error);
    throw error;
  }
};

export interface EditOrderData {
  number?: number;
  price?: number;
  collectionId?: string;
}

export const editOrder = async (
  orderId: string,
  updates: EditOrderData,
): Promise<void> => {
  try {
    if (!orderId) {
      throw new Error('Order ID is required');
    }

    if (Object.keys(updates).length === 0) {
      throw new Error('At least one field must be updated');
    }

    if (updates.price !== undefined && updates.price <= 0) {
      throw new Error('Price must be greater than 0');
    }

    if (updates.number !== undefined && updates.number <= 0) {
      throw new Error('Order number must be greater than 0');
    }

    const orderRef = doc(firestore, ORDERS_COLLECTION_NAME, orderId);
    const updateData: any = {
      ...updates,
      updatedAt: new Date(),
    };

    await updateDoc(orderRef, updateData);
  } catch (error) {
    console.error('Error editing order:', error);
    throw error;
  }
};

export const increaseAllUserBalances = async (
  increaseAmount: number,
): Promise<{ updatedCount: number; errors: string[] }> => {
  try {
    if (increaseAmount <= 0) {
      throw new Error('Increase amount must be greater than 0');
    }

    const usersQuery = query(collection(firestore, APP_USERS_COLLECTION));
    const usersSnapshot = await getDocs(usersQuery);

    const batch = writeBatch(firestore);
    const errors: string[] = [];
    let updatedCount = 0;

    usersSnapshot.forEach((userDoc) => {
      try {
        const userData = userDoc.data() as UserEntity;
        const currentBalance = userData.balance?.sum ?? 0;
        const newBalance = currentBalance + increaseAmount;

        const userRef = doc(firestore, APP_USERS_COLLECTION, userDoc.id);
        batch.update(userRef, {
          'balance.sum': newBalance,
          updatedAt: new Date(),
        });

        updatedCount++;
      } catch (error) {
        errors.push(`Failed to update user ${userDoc.id}: ${error}`);
      }
    });

    await batch.commit();

    return { updatedCount, errors };
  } catch (error) {
    console.error('Error increasing user balances:', error);
    throw error;
  }
};

export const topUpUserBalance = async (
  userId: string,
  amount: number,
): Promise<{ success: boolean; message: string; newBalance: number }> => {
  try {
    if (!userId) {
      throw new Error('User ID is required');
    }

    if (amount <= 0) {
      throw new Error('Amount must be greater than 0');
    }

    // Get user document reference
    const userRef = doc(firestore, APP_USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      throw new Error('User not found');
    }

    const userData = userDoc.data() as UserEntity;
    const currentBalance = userData.balance?.sum ?? 0;
    const newBalance = currentBalance + amount;

    // Update user balance directly in Firestore
    await updateDoc(userRef, {
      'balance.sum': newBalance,
      updatedAt: new Date(),
    });

    const userName =
      userData.displayName ?? userData.telegram_handle ?? 'Unknown User';

    return {
      success: true,
      message: `Successfully added ${amount} TON to ${userName}'s balance`,
      newBalance,
    };
  } catch (error) {
    console.error('Error topping up user balance:', error);
    throw error;
  }
};

export const decreaseUserBalance = async (
  userId: string,
  amount: number,
): Promise<{ success: boolean; message: string; newBalance: number }> => {
  try {
    if (!userId) {
      throw new Error('User ID is required');
    }

    if (amount <= 0) {
      throw new Error('Amount must be greater than 0');
    }

    // Get user document reference
    const userRef = doc(firestore, APP_USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      throw new Error('User not found');
    }

    const userData = userDoc.data() as UserEntity;
    const currentBalance = userData.balance?.sum ?? 0;
    const newBalance = Math.max(0, currentBalance - amount);

    // Update user balance directly in Firestore
    await updateDoc(userRef, {
      'balance.sum': newBalance,
      updatedAt: new Date(),
    });

    const userName =
      userData.displayName ?? userData.telegram_handle ?? 'Unknown User';

    return {
      success: true,
      message: `Successfully decreased ${userName}'s balance by ${amount} TON`,
      newBalance,
    };
  } catch (error) {
    console.error('Error decreasing user balance:', error);
    throw error;
  }
};

export const resetUserBalance = async (
  userId: string,
): Promise<{ success: boolean; message: string }> => {
  try {
    if (!userId) {
      throw new Error('User ID is required');
    }

    // Get user document reference
    const userRef = doc(firestore, APP_USERS_COLLECTION, userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      throw new Error('User not found');
    }

    const userData = userDoc.data() as UserEntity;

    // Reset both sum and locked to 0
    await updateDoc(userRef, {
      'balance.sum': 0,
      'balance.locked': 0,
      updatedAt: new Date(),
    });

    const userName =
      userData.displayName ?? userData.telegram_handle ?? 'Unknown User';

    return {
      success: true,
      message: `Successfully reset ${userName}'s balance (sum=0, locked=0)`,
    };
  } catch (error) {
    console.error('Error resetting user balance:', error);
    throw error;
  }
};

export interface RecalculateDeadlinesResponse {
  success: boolean;
  message: string;
  updatedCount: number;
  newDeadline?: Date;
}

export const recalculateOrderDeadlines = async (
  collectionId: string,
): Promise<RecalculateDeadlinesResponse> => {
  try {
    if (!collectionId) {
      throw new Error('Collection ID is required');
    }

    const recalculateDeadlines = httpsCallable(
      firebaseFunctions,
      AppCloudFunctions.recalculateOrderDeadlines,
    );

    const result = await recalculateDeadlines({ collectionId });
    return result.data as RecalculateDeadlinesResponse;
  } catch (error) {
    console.error('Error recalculating order deadlines:', error);
    throw error;
  }
};

export interface ClearDeadlinesResponse {
  success: boolean;
  message: string;
  updatedCount: number;
}

export const clearOrderDeadlines = async (
  collectionId: string,
): Promise<ClearDeadlinesResponse> => {
  try {
    if (!collectionId) {
      throw new Error('Collection ID is required');
    }

    const clearDeadlines = httpsCallable(
      firebaseFunctions,
      AppCloudFunctions.clearOrderDeadlines,
    );

    const result = await clearDeadlines({ collectionId });
    return result.data as ClearDeadlinesResponse;
  } catch (error) {
    console.error('Error clearing order deadlines:', error);
    throw error;
  }
};

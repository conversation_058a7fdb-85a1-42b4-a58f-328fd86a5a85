{"name": "marketplace-bot", "version": "1.0.0", "description": "Telegram bot for the marketplace platform", "main": "dist/index.js", "scripts": {"start": "NODE_ENV=production node dist/index.js", "start:dev": "NODE_ENV=development node dist/index.js", "dev": "NODE_ENV=development nodemon src/index.ts", "build": "tsc", "build:watch": "tsc --watch", "clean": "rm -rf dist", "setup:webhook": "./scripts/setup-webhook.sh", "kill-bot": "./scripts/kill-bot.sh", "restart": "npm run kill-bot && npm run dev"}, "keywords": ["telegram", "bot", "marketplace", "typescript"], "author": "Marketplace Team", "license": "ISC", "type": "commonjs", "dependencies": {"@google-cloud/logging-winston": "^6.0.1", "@telegram-apps/sdk-react": "^3.3.1", "@types/express": "^5.0.3", "@types/redis": "^4.0.10", "axios": "^1.10.0", "dotenv": "^16.5.0", "eruda": "^3.4.3", "express": "^5.1.0", "redis": "^5.5.6", "telegraf": "^4.16.3", "winston": "^3.17.0"}, "devDependencies": {"@types/node": "^24.0.3", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}
'use client';

import { useIntl } from 'react-intl';

import { socialLinksMessages } from './intl/social-links.messages';

interface SocialLink {
  name: string;
  url: string;
  icon: React.ReactNode;
}

const socials: SocialLink[] = [
  {
    name: 'Telegram',
    url: 'https://t.me/prem_channel',
    icon: (
      <svg className="w-8 h-8" viewBox="0 0 24 24" fill="white">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4.64 6.8c-.16 1.56-.86 5.24-1.15 6.96-.13.73-.38.98-.63 1.01-.53.05-.93-.35-1.44-.68-.8-.52-1.25-.84-2.02-1.35-.89-.59-.31-.91.2-1.44.13-.14 2.46-2.25 2.5-2.44 0-.02.01-.11-.04-.16-.05-.05-.13-.03-.19-.02-.08.02-1.36.86-3.84 2.53-.36.25-.69.37-.98.36-.32-.01-.95-.18-1.41-.33-.57-.19-1.02-.28-.98-.59.02-.16.25-.33.68-.5 2.65-1.15 4.42-1.91 5.3-2.28 2.53-1.05 3.05-1.23 3.39-1.24z" />
      </svg>
    ),
  },
  {
    name: 'X',
    url: 'https://x.com/prem_on_ton',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
      </svg>
    ),
  },
];

export function SocialLinks() {
  const { formatMessage: t } = useIntl();

  const handleSocialClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className="p-4 bg-[#232e3c] rounded-2xl">
      <h3 className="text-[#f5f5f5] font-semibold text-lg mb-4 text-center">
        {t(socialLinksMessages.followUs)}
      </h3>

      <div className="flex justify-center gap-4">
        {socials.map((social) => (
          <button
            key={social.name}
            onClick={() => handleSocialClick(social.url)}
            className="flex items-center justify-center w-12 h-12 bg-[#6ab2f2] rounded-full text-white hover:bg-[#5a9fd9] transition-colors duration-200 shadow-lg hover:shadow-xl"
            aria-label={t(socialLinksMessages.followUsOn, {
              platform: social.name,
            })}
          >
            {social.icon}
          </button>
        ))}
      </div>
    </div>
  );
}

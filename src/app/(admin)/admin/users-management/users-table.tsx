import { Avatar } from '@telegram-apps/telegram-ui';
import { <PERSON><PERSON>, Loader2, User } from 'lucide-react';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { UserEntity } from '@/core.constants';
import { getBalanceInfo } from '@/services/user-service';

interface UsersTableProps {
  users: UserEntity[];
  loading: boolean;
}

export function UsersTable({ users, loading }: UsersTableProps) {
  const formatTonAddress = (address?: string) => {
    if (!address) return 'Not connected';
    return `${address.slice(0, 6)}...${address.slice(-6)}`;
  };

  const formatTelegramHandle = (handle?: string) => {
    if (!handle) return '-';
    return `@${handle}`;
  };

  const formatBalance = (user: UserEntity) => {
    if (!user.balance) return '0.00 TON';
    const { availableBalance } = getBalanceInfo(user);
    return `${availableBalance.toFixed(2)} TON`;
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You might want to add a toast notification here
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin text-[#6ab2f2]" />
        <span className="ml-2 text-[#708499]">Loading users...</span>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Avatar</TableHead>
            <TableHead>Display Name</TableHead>
            <TableHead>Telegram Handle</TableHead>
            <TableHead>TON Address</TableHead>
            <TableHead>Balance</TableHead>
            <TableHead>Role</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id}>
              <TableCell>
                <div className="w-10 h-10 rounded-full overflow-hidden bg-[#232e3c] flex items-center justify-center">
                  {user.photoURL ? (
                    <Avatar size={40} src={user.photoURL} />
                  ) : (
                    <User className="w-5 h-5 text-[#708499]" />
                  )}
                </div>
              </TableCell>
              <TableCell>
                <div className="flex flex-col">
                  <span className="font-medium">
                    {user.displayName || user.name || 'Anonymous User'}
                  </span>
                  {user.tg_id && (
                    <span className="text-xs text-[#708499]">
                      ID: {user.tg_id}
                    </span>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <span className="text-sm">
                  {formatTelegramHandle(user.telegram_handle)}
                </span>
              </TableCell>
              <TableCell>
                {user.ton_wallet_address ? (
                  <button
                    onClick={() => copyToClipboard(user.ton_wallet_address!)}
                    className="flex items-center gap-2 text-sm font-mono hover:text-[#6ab2f2] transition-colors group cursor-pointer"
                    title="Click to copy address"
                  >
                    <span>{formatTonAddress(user.ton_wallet_address)}</span>
                    <Copy className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </button>
                ) : (
                  <span className="text-sm font-mono">Not connected</span>
                )}
              </TableCell>
              <TableCell>
                <span className="text-sm font-mono text-[#6ab2f2]">
                  {formatBalance(user)}
                </span>
              </TableCell>
              <TableCell>
                <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    user.role === 'admin'
                      ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
                  }`}
                >
                  {user.role || 'user'}
                </span>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}

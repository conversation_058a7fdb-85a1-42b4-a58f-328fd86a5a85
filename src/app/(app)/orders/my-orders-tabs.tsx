'use client';

import { TabsList } from '@telegram-apps/telegram-ui';
import { TabsItem } from '@telegram-apps/telegram-ui/dist/components/Navigation/TabsList/components/TabsItem/TabsItem';
import { useIntl } from 'react-intl';

import { myOrdersTabsMessages } from './intl/my-orders-tabs.messages';

export type MyOrdersTabType = 'buy' | 'sell';

interface MyOrdersTabsProps {
  activeTab: MyOrdersTabType;
  onTabChange: (tab: MyOrdersTabType) => void;
  buyOrdersCount: number;
  sellOrdersCount: number;
}

export const MyOrdersTabs = ({
  activeTab,
  onTabChange,
  buyOrdersCount,
  sellOrdersCount,
}: MyOrdersTabsProps) => {
  const { formatMessage: t } = useIntl();

  return (
    <TabsList className="grid w-full grid-cols-2 gap-0!">
      <TabsItem
        selected={activeTab === 'buy'}
        onClick={() => onTabChange('buy')}
      >
        {t(myOrdersTabsMessages.myBuyOrders, { count: buyOrdersCount })}
      </TabsItem>
      <TabsItem
        selected={activeTab === 'sell'}
        onClick={() => onTabChange('sell')}
      >
        {t(myOrdersTabsMessages.mySellOrders, { count: sellOrdersCount })}
      </TabsItem>
    </TabsList>
  );
};

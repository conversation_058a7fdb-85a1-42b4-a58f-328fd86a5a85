import { useState } from 'react';

import { increaseAllUserBalances } from '@/api/admin-api';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';

interface IncreaseUserBalancesModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function IncreaseUserBalancesModal({
  isOpen,
  onClose,
}: IncreaseUserBalancesModalProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [increaseAmount, setIncreaseAmount] = useState('');

  const handleInputChange = (value: string) => {
    const sanitizedValue = value.replace(/[^0-9.]/g, '');
    setIncreaseAmount(sanitizedValue);
  };

  const handleSubmit = async () => {
    const amount = parseFloat(increaseAmount);

    if (!amount || amount <= 0) {
      toast({
        title: 'Invalid Amount',
        description: 'Please enter a valid amount greater than 0',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);

    try {
      const result = await increaseAllUserBalances(amount);

      toast({
        title: 'Success',
        description: `Successfully increased balances for ${result.updatedCount} users by ${amount} TON${result.errors.length > 0 ? `. ${result.errors.length} errors occurred.` : ''}`,
      });

      if (result.errors.length > 0) {
        console.error('Errors during balance increase:', result.errors);
      }

      setIncreaseAmount('');
      onClose();
    } catch (error) {
      console.error('Error increasing user balances:', error);
      toast({
        title: 'Error',
        description: 'Failed to increase user balances. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setIncreaseAmount('');
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Increase User Balances</DialogTitle>
          <DialogDescription>
            Increase the balance.sum field for all users in the system. This
            action will affect all users.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="increaseAmount">Amount to increase (TON)</Label>
            <Input
              id="increaseAmount"
              type="text"
              placeholder="Enter amount (e.g., 10.5)"
              value={increaseAmount}
              onChange={(e) => handleInputChange(e.target.value)}
              disabled={loading}
              className="border-2 border-gray-300 focus:border-blue-500"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading || !increaseAmount}>
            {loading ? 'Processing...' : 'Increase Balances'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

'use client';

import { Loader2 } from 'lucide-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';

import { TelegramIcon } from '@/components/icons/telegram-icon';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useTelegramAuth } from '@/hooks/use-telegram-auth';
import { useRootContext } from '@/root-context';

import { loginModalMessages } from './login-modal.messages';

interface LoginModalProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  title?: string;
  description?: string;
}

export function LoginModal({
  open = true,
  onOpenChange,
  title,
  description,
}: LoginModalProps) {
  const { formatMessage: t } = useIntl();
  const { currentUser, refetchUser } = useRootContext();
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  const defaultTitle = title || t(loginModalMessages.authenticationRequired);
  const defaultDescription =
    description || t(loginModalMessages.mustBeLoggedIn);

  const { authenticate } = useTelegramAuth({
    onSuccess: async () => {
      try {
        await refetchUser();
        onOpenChange?.(false);
      } catch (error) {
        console.error('Error refetching user after auth:', error);
      }
    },
    onError: (error) => {
      console.error('Authentication failed:', error);
      setIsAuthenticating(false);
    },
  });

  const handleSignIn = async () => {
    setIsAuthenticating(true);
    try {
      await authenticate();
    } catch (error) {
      console.error('Authentication failed:', error);
      setIsAuthenticating(false);
    }
  };

  if (currentUser) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md max-w-[90vw] rounded-2xl">
        <DialogHeader>
          <DialogTitle className="text-center">{defaultTitle}</DialogTitle>
          <DialogDescription className="text-center">
            {defaultDescription}
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col items-center gap-6 py-6">
          <button
            onClick={handleSignIn}
            disabled={isAuthenticating}
            className="w-full bg-[#229ED9] hover:bg-[#229ED9] text-white font-medium rounded-xl px-6 py-3 flex items-center justify-center gap-3 transition-all duration-200 disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {isAuthenticating ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                {t(loginModalMessages.signingIn)}
              </>
            ) : (
              <>
                <TelegramIcon className="w-5 h-5" />
                {t(loginModalMessages.signInWithTelegram)}
              </>
            )}
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

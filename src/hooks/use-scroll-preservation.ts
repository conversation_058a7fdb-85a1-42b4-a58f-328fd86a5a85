'use client';

import { useEffect } from 'react';

interface UseScrollPreservationProps {
  isOpen: boolean;
}

export function useScrollPreservation({ isOpen }: UseScrollPreservationProps) {
  useEffect(() => {
    if (isOpen) {
      const scrollY = window.scrollY;
      document.documentElement.style.setProperty(
        '--scroll-y',
        scrollY.toString(),
      );

      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';
      document.body.style.overflow = 'hidden';
    } else {
      const scrollY =
        document.documentElement.style.getPropertyValue('--scroll-y');

      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      document.body.style.overflow = '';

      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY, 10));
      }

      document.documentElement.style.removeProperty('--scroll-y');
    }
  }, [isOpen]);
}
